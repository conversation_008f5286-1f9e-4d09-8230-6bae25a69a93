#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
孤立拐点特殊处理测试脚本
测试不同数量孤立拐点的特殊处理逻辑
"""

import numpy as np
import math

def calculate_distance(point1, point2):
    """计算两点之间的欧几里得距离"""
    return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)

def calculate_polygon_area(points):
    """使用鞋带公式计算多边形面积"""
    if len(points) < 3:
        return 0.0
        
    area = 0.0
    n = len(points)
    
    for i in range(n):
        j = (i + 1) % n
        area += points[i][0] * points[j][1]
        area -= points[j][0] * points[i][1]
        
    return abs(area) / 2.0

def calculate_point_to_line_distance(point, line_start, line_end):
    """计算点到直线的垂直距离"""
    try:
        x1, y1 = line_start
        x2, y2 = line_end
        px, py = point
        
        if x1 == x2 and y1 == y2:
            return calculate_distance(point, line_start)
            
        a = y2 - y1
        b = x1 - x2
        c = (x2 - x1) * y1 - (y2 - y1) * x1
        
        distance = abs(a * px + b * py + c) / np.sqrt(a * a + b * b)
        return distance
        
    except Exception as e:
        print(f"计算点到直线距离时出错: {e}")
        return 0.0

def test_single_isolated_corner():
    """测试单个孤立拐点的处理"""
    print("\n" + "=" * 60)
    print("测试情况1：单个孤立拐点处理")
    print("=" * 60)
    
    # 模拟数据
    vertices = [(100, 100), (150, 100), (200, 150), (150, 200), (100, 200), (50, 150)]
    corner_marks = [0, 1, 2, 0, 1, 0]  # 索引2是孤立拐点
    isolated_index = 2
    
    # 模拟内轮廓数据
    inner_contours = [
        np.array([[[120, 120]], [[130, 120]], [[140, 130]], [[130, 140]], [[120, 140]]])
    ]
    
    print(f"顶点: {vertices}")
    print(f"拐点标记: {corner_marks}")
    print(f"孤立拐点索引: {isolated_index}")
    
    # 模拟处理逻辑
    isolated_point = vertices[isolated_index]
    print(f"孤立拐点坐标: {isolated_point}")
    
    # 找前后白点
    prev_white_index = 1  # 索引1标记为1，但我们假设找到的是0
    next_white_index = 3  # 索引3标记为0
    
    prev_white_point = vertices[prev_white_index]
    next_white_point = vertices[next_white_index]
    print(f"前白点: 索引{prev_white_index}, 坐标{prev_white_point}")
    print(f"后白点: 索引{next_white_index}, 坐标{next_white_point}")
    
    # 模拟找到的内点
    inner_point = (125, 125)
    print(f"内点坐标: {inner_point}")
    
    # 计算四边形面积
    quadrilateral_points = [isolated_point, prev_white_point, inner_point, next_white_point]
    quad_area = calculate_polygon_area(quadrilateral_points)
    print(f"四边形面积: {quad_area:.2f}")
    
    # 计算垂直距离
    dist1 = calculate_point_to_line_distance(inner_point, isolated_point, prev_white_point)
    dist2 = calculate_point_to_line_distance(inner_point, isolated_point, next_white_point)
    print(f"内点到延长线1的距离: {dist1:.2f}")
    print(f"内点到延长线2的距离: {dist2:.2f}")
    
    # 计算正方形面积
    square_area1 = dist1 * dist1
    square_area2 = dist2 * dist2
    print(f"正方形1面积: {square_area1:.2f}")
    print(f"正方形2面积: {square_area2:.2f}")
    
    # 选择结果
    if square_area1 > quad_area and square_area1 >= square_area2:
        selected_edge = dist1
        print(f"✓ 选择正方形1的边长: {selected_edge:.2f}")
    elif square_area2 > quad_area:
        selected_edge = dist2
        print(f"✓ 选择正方形2的边长: {selected_edge:.2f}")
    else:
        print("✗ 所有正方形面积都不大于四边形面积")

def test_two_isolated_corners():
    """测试两个孤立拐点的处理"""
    print("\n" + "=" * 60)
    print("测试情况2：两个孤立拐点处理")
    print("=" * 60)
    
    # 模拟数据
    vertices = [(100, 100), (150, 100), (200, 150), (150, 200), (100, 200), (50, 150)]
    corner_marks = [0, 2, 0, 2, 0, 1]  # 索引1和3是孤立拐点
    isolated_corners = [1, 3]
    
    print(f"顶点: {vertices}")
    print(f"拐点标记: {corner_marks}")
    print(f"孤立拐点索引: {isolated_corners}")
    
    point1 = vertices[isolated_corners[0]]
    point2 = vertices[isolated_corners[1]]
    print(f"孤立拐点1: 索引{isolated_corners[0]}, 坐标{point1}")
    print(f"孤立拐点2: 索引{isolated_corners[1]}, 坐标{point2}")
    
    # 计算连接线段长度
    line_length = calculate_distance(point1, point2)
    print(f"连接线段长度: {line_length:.2f}")
    
    # 模拟检查白点结果
    has_white_points = True  # 假设有白点
    print(f"线段上是否有白点: {has_white_points}")
    
    if has_white_points:
        result = line_length
        print(f"✓ 使用原始长度: {result:.2f}")
    else:
        result = line_length / np.sqrt(2)
        print(f"✓ 使用长度/√2: {result:.2f}")

def test_three_isolated_corners():
    """测试三个孤立拐点的处理"""
    print("\n" + "=" * 60)
    print("测试情况3：三个孤立拐点处理")
    print("=" * 60)
    
    # 模拟数据
    vertices = [(100, 100), (150, 100), (200, 150), (150, 200), (100, 200), (50, 150)]
    corner_marks = [2, 0, 2, 0, 2, 0]  # 索引0、2、4是孤立拐点
    isolated_corners = [0, 2, 4]
    
    print(f"顶点: {vertices}")
    print(f"拐点标记: {corner_marks}")
    print(f"孤立拐点索引: {isolated_corners}")
    
    # 按索引排序，找到中间的拐点
    sorted_corners = sorted(isolated_corners)
    middle_index = sorted_corners[1]
    first_index = sorted_corners[0]
    last_index = sorted_corners[2]
    
    print(f"排序后的孤立拐点: {sorted_corners}")
    print(f"忽略中间拐点: {middle_index}")
    print(f"连接拐点: {first_index} - {last_index}")
    
    point1 = vertices[first_index]
    point2 = vertices[last_index]
    print(f"拐点1坐标: {point1}")
    print(f"拐点2坐标: {point2}")
    
    # 计算连接线段长度
    line_length = calculate_distance(point1, point2)
    print(f"✓ 连接线段长度: {line_length:.2f}")

def test_algorithm_summary():
    """显示算法总结"""
    print("\n" + "=" * 60)
    print("孤立拐点特殊处理算法总结")
    print("=" * 60)
    
    print("🔵 孤立拐点定义:")
    print("  - 当前拐点重叠度 >= 阈值（标记为1）")
    print("  - 前后相邻拐点重叠度 < 阈值（标记为0）")
    print("  - 满足条件时标记为2（孤立拐点）")
    print()
    
    print("📋 特殊处理规则:")
    print("  1️⃣ 孤立拐点数量 = 1:")
    print("     - 找前后相邻白点和内轮廓最近点")
    print("     - 构成四边形计算面积")
    print("     - 计算内点到两条延长线的垂直距离")
    print("     - 选择面积更大的正方形边长")
    print()
    print("  2️⃣ 孤立拐点数量 = 2:")
    print("     - 连接两个孤立拐点")
    print("     - 检查线段上是否有白点（容差0-4像素）")
    print("     - 有白点：使用原始长度")
    print("     - 无白点：使用长度/√2")
    print()
    print("  3️⃣ 孤立拐点数量 = 3:")
    print("     - 忽略序列中间的孤立拐点")
    print("     - 连接另外两个孤立拐点")
    print("     - 使用连接线段长度")
    print()
    
    print("🎯 处理时机:")
    print("  - 在找出所有有效边之后")
    print("  - 在计算最小边长之前")
    print("  - 将特殊处理得到的边长加入有效边列表")

if __name__ == "__main__":
    # 运行所有测试
    test_single_isolated_corner()
    test_two_isolated_corners()
    test_three_isolated_corners()
    test_algorithm_summary()
    
    print("\n🎉 孤立拐点特殊处理算法测试完成！")
    print("💡 提示：实际运行时会根据孤立拐点数量自动选择相应的处理策略。")
