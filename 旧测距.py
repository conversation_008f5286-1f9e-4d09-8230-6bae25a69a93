import time
from media.sensor import *
from media.display import *
from media.media import *

# ---------- 可调整的参数 ----------
LCD_W      = 800
LCD_H      = 480
A4_REAL_W  = 210.0
FOCAL_PIX  = 322.5
# ----------------------------------


sensor = Sensor(width=640, height=480)
sensor.reset()
sensor.set_framesize(width=320, height=240)
sensor.set_pixformat(Sensor.RGB565)


Display.init(Display.ST7701, width=LCD_W, height=LCD_H, to_ide=True)
MediaManager.init()
sensor.run()


clk = time.clock()

def is_a4_like(rect):
    w, h = rect.w(), rect.h()
    ratio = max(w, h) / min(w, h)
    return 1.25 < ratio < 1.55


def inside(inner, outer):
    xo, yo, wo, ho = outer.rect()
    for (x, y) in inner.corners():
        if not (xo < x < xo + wo and yo < y < yo + ho):
            return False
    return True


while True:
    clk.tick()
    img = sensor.snapshot()

    rects = img.find_rects(threshold=10000)

    rects = sorted(rects, key=lambda r: r.w() * r.h(), reverse=True)

    outer = inner = None
    if len(rects) >= 2:
        cand = [r for r in rects[:2] if is_a4_like(r)]
        if len(cand) == 2:
            outer, inner = cand[0], cand[1]
            if not inside(inner, outer):
                outer = inner = None

    dist_mm = 0
    if outer and inner:
        img.draw_rectangle(outer.rect(), color=(255, 0, 0), thickness=2)
        for p in outer.corners():
            img.draw_circle(p[0], p[1], 5, color=(0, 255, 0))
        img.draw_rectangle(inner.rect(), color=(0, 255, 255), thickness=2)
        for p in inner.corners():
            img.draw_circle(p[0], p[1], 5, color=(0, 0, 255))

        pixel_width = outer.w()
        dist_mm = (A4_REAL_W * FOCAL_PIX) / pixel_width
        img.draw_string(5, 5, "Dist:%.1f cm" % (dist_mm / 10),
                        color=(255, 255, 255), scale=2)

    Display.show_image(img,
                       x=(LCD_W - img.width()) // 2,
                       y=(LCD_H - img.height()) // 2)


