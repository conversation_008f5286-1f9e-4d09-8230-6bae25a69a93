#
# Copyright (c) 2023 Canaan Inc.
#
# SPDX-License-Identifier: Apache-2.0
#

import time
from media.sensor import *
from media.display import *
from media.media import *
import math

# ==============================================================================
# ---------- 1. 全局参数配置 ----------
# ==============================================================================

# -- 显示与相机 --
LCD_W, LCD_H = 800, 480
SENSOR_W, SENSOR_H = 320, 240

# -- 测距参数 --
A4_REAL_W = 210.0
FOCAL_PIX = 322.5
ROI_FIND_THRESHOLD = 10000

# -- 优化参数 --
SMOOTHING_ALPHA = 0.4
DETECTION_TIMEOUT = 10

# ==============================================================================
# ---------- 2. 辅助类与函数 ----------
# ==============================================================================

class ValueSmoother:
    """一个简单的指数移动平均(EMA)平滑器，用于减少测量值的抖动"""
    def __init__(self, alpha=0.5):
        self.alpha, self.smoothed_value = alpha, None
    def update(self, measurement):
        if self.smoothed_value is None: self.smoothed_value = measurement
        else: self.smoothed_value = self.alpha * measurement + (1 - self.alpha) * self.smoothed_value
        return self.get()
    def get(self):
        return int(self.smoothed_value) if self.smoothed_value is not None else 0
    def reset(self):
        self.smoothed_value = None

def get_distance(p1, p2):
    """计算两点之间的欧几里得距离"""
    return math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

# ========== 新增核心函数：计算平均宽度以抵抗透视形变 ==========
def calculate_average_width(rect):
    """
    计算检测到的矩形（可能是梯形）上下两条边的平均长度。
    这比使用bounding box的宽度更准确，尤其是在物体倾斜时。
    """
    corners = rect.corners() # 获取四个角点, 通常顺序是 top-left, top-right, bottom-right, bottom-left

    # 假设角点顺序是固定的
    top_left = corners[0]
    top_right = corners[1]
    bottom_right = corners[2]
    bottom_left = corners[3]

    # 计算顶部和底部边缘的长度
    top_edge_width = get_distance(top_left, top_right)
    bottom_edge_width = get_distance(bottom_left, bottom_right)

    # 返回两条边的平均长度
    return (top_edge_width + bottom_edge_width) / 2.0

def is_a4_like(rect):
    w, h = rect.w(), rect.h()
    if min(w, h) == 0: return False
    ratio = max(w, h) / min(w, h)
    return 1.25 < ratio < 1.55

def inside(inner, outer):
    xo, yo, wo, ho = outer.rect()
    for (x, y) in inner.corners():
        if not (xo < x < xo + wo and yo < y < yo + ho):
            return False
    return True

# ==============================================================================
# ---------- 3. 硬件与变量初始化 ----------
# ==============================================================================

sensor = Sensor(width=640, height=480); sensor.reset()
sensor.set_framesize(width=SENSOR_W, height=SENSOR_H)
sensor.set_pixformat(Sensor.RGB565)

Display.init(Display.ST7701, width=LCD_W, height=LCD_H, to_ide=True)
MediaManager.init(); sensor.run()

clk = time.clock()
width_smoother = ValueSmoother(alpha=SMOOTHING_ALPHA)
frames_since_detection = 0

# ==============================================================================
# ---------- 4. 最终优化的主循环 ----------
# ==============================================================================

while True:
    clk.tick()
    img = sensor.snapshot()

    rects = sorted(img.find_rects(threshold=ROI_FIND_THRESHOLD), key=lambda r: r.w() * r.h(), reverse=True)

    outer = inner = None
    if len(rects) >= 2:
        cand = [r for r in rects[:2] if is_a4_like(r)]
        if len(cand) == 2 and inside(cand[1], cand[0]):
            outer, inner = cand[0], cand[1]

    dist_mm = 0
    if outer and inner:
        frames_since_detection = 0

        # 1. (核心改进) 使用新的函数计算平均宽度，而不是包围盒宽度
        raw_pixel_width = calculate_average_width(outer)

        # 2. (保留优化) 使用平滑器更新这个更准确的宽度值
        smoothed_pixel_width = width_smoother.update(raw_pixel_width)

        # 3. 使用【平滑后的平均宽度】来计算距离
        if smoothed_pixel_width > 0:
            dist_mm = (A4_REAL_W * FOCAL_PIX) / smoothed_pixel_width

        # --- 绘制部分 ---
        # 绘制精确的四边形轮廓，而不是矩形包围盒
        for i in range(4):
            p1 = outer.corners()[i]
            p2 = outer.corners()[(i + 1) % 4]
            img.draw_line(p1[0], p1[1], p2[0], p2[1], color=(0, 255, 0), thickness=2)

        img.draw_rectangle(inner.rect(), color=(0, 255, 255), thickness=1) # 内框保持不变

        display_text = "Dist: %.1f cm" % (dist_mm / 10)
        img.draw_string(5, 5, display_text, color=(0, 255, 0), scale=2)

    else:
        frames_since_detection += 1
        if frames_since_detection > DETECTION_TIMEOUT:
            width_smoother.reset()

        smoothed_width = width_smoother.get()
        if smoothed_width > 0:
            last_dist_mm = (A4_REAL_W * FOCAL_PIX) / smoothed_width
            display_text = "Dist: %.1f cm (Searching...)" % (last_dist_mm / 10)
            img.draw_string(5, 5, display_text, color=(255, 165, 0), scale=2)
        else:
            img.draw_string(5, 5, "Searching for A4 paper...", color=(255, 0, 0), scale=2)

    Display.show_image(img, x=(LCD_W - img.width()) // 2, y=(LCD_H - img.height()) // 2)
