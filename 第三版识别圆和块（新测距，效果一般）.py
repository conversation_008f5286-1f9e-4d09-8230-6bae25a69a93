#
# Copyright (c) 2023 Canaan Inc.
#
# SPDX-License-Identifier: Apache-2.0
#

import time
from media.sensor import *
from media.display import *
from media.media import *
import math
import gc

# ==============================================================================
# ---------- 1. 全局参数配置 ----------
# ==============================================================================

# -- 显示与相机 --
LCD_W = 800
LCD_H = 480
SENSOR_W = 640
SENSOR_H = 480

# -- A4纸测距与ROI定位参数 --
A4_REAL_W = 210.0
FOCAL_PIX = 670
ROI_FIND_THRESHOLD = 10000

# -- ROI内部形状识别参数 --
SHAPE_FIND_THRESHOLD = 8000
CIRCLE_FIND_THRESHOLD = 3000
MIN_SHAPE_AREA = 50

# -- 形状跟踪与平滑参数 --
SHAPE_SMOOTHING_ALPHA = 0.6 # <--- 用于内部形状的平滑因子
POSITION_TOLERANCE = 25
SHAPE_TIMEOUT = 15

# -- 新增：精确测距的优化参数 --
WIDTH_SMOOTHING_ALPHA = 0.4 # <--- 用于测距宽度的平滑因子，值更小更稳定
DETECTION_TIMEOUT = 10

# -- 显示控制 --
SHOW_AREA = True
SHOW_EDGE_LENGTH = True
SHOW_RADIUS = True

# ==============================================================================
# ---------- 2. 辅助类与函数 ----------
# ==============================================================================

class ValueSmoother:
    def __init__(self, alpha=0.5):
        self.alpha, self.smoothed_value = alpha, None
    def update(self, measurement):
        if self.smoothed_value is None: self.smoothed_value = measurement
        else: self.smoothed_value = self.alpha * measurement + (1 - self.alpha) * self.smoothed_value
        return self.get()
    def get(self):
        if isinstance(self.smoothed_value, tuple): return tuple(int(v) for v in self.smoothed_value)
        return int(self.smoothed_value) if self.smoothed_value is not None else 0
    def reset(self):
        self.smoothed_value = None

def get_distance(p1, p2):
    return math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

# ========== 核心改进：计算平均宽度以抵抗透视形变 ==========
def calculate_average_width(rect):
    corners = rect.corners()
    top_edge_width = get_distance(corners[0], corners[1])
    bottom_edge_width = get_distance(corners[3], corners[2])
    return (top_edge_width + bottom_edge_width) / 2.0

def is_a4_like(rect):
    w, h = rect.w(), rect.h()
    if min(w, h) == 0: return False
    ratio = max(w, h) / min(w, h)
    return 1.25 < ratio < 1.55

def inside(inner, outer):
    xo, yo, wo, ho = outer.rect()
    for (x, y) in inner.corners():
        if not (xo < x < xo + wo and yo < y < yo + ho): return False
    return True

# ==============================================================================
# ---------- 3. 硬件与变量初始化 ----------
# ==============================================================================

sensor = Sensor(width=640, height=480); sensor.reset()
sensor.set_framesize(width=SENSOR_W, height=SENSOR_H)
sensor.set_pixformat(Sensor.RGB565)

Display.init(Display.ST7701, width=LCD_W, height=LCD_H, to_ide=True)
MediaManager.init(); sensor.run()

clk = time.clock(); frame_count = 0
tracked_shapes = {}; shape_id_counter = 0

# -- 新增：用于精确测距的平滑器和计数器 --
width_smoother = ValueSmoother(alpha=WIDTH_SMOOTHING_ALPHA)
frames_since_detection = 0

# ==============================================================================
# ---------- 4. 主循环 ----------
# ==============================================================================

while True:
    clk.tick()
    gc.collect()
    img = sensor.snapshot()
    frame_count += 1

    active_roi_tuple, outer_roi_rect, inner_roi_rect = None, None, None

    all_rects_for_roi_finding = sorted(img.find_rects(threshold=ROI_FIND_THRESHOLD), key=lambda r: r.w() * r.h(), reverse=True)
    if len(all_rects_for_roi_finding) >= 2:
        candidates = [r for r in all_rects_for_roi_finding[:2] if is_a4_like(r)]
        if len(candidates) == 2 and inside(candidates[1], candidates[0]):
            outer_roi_rect, inner_roi_rect = candidates[0], candidates[1]
            active_roi_tuple = outer_roi_rect.rect()

    if active_roi_tuple:
        # 在ROI内部进行形状检测 (这部分逻辑保持不变)
        detected_circles_inside_roi = img.find_circles(roi=active_roi_tuple, threshold=CIRCLE_FIND_THRESHOLD, r_min=5)
        detected_rects_inside_roi = img.find_rects(roi=active_roi_tuple, threshold=SHAPE_FIND_THRESHOLD)

        # --- 更新内部形状的跟踪逻辑 (保持不变) ---
        for c in detected_circles_inside_roi:
            # ... (内部圆形跟踪逻辑) ...
            if math.pi * c.r()**2 < MIN_SHAPE_AREA: continue
            cx, cy, radius = c.x(), c.y(), c.r()
            matched_key = None; min_dist = POSITION_TOLERANCE
            for key, data in tracked_shapes.items():
                if data['type'] == 'circle' and get_distance((cx, cy), data['pos']) < min_dist:
                    min_dist, matched_key = get_distance((cx, cy), data['pos']), key
            if matched_key:
                tracked_shapes[matched_key].update({'pos':(cx,cy), 'last_seen':frame_count})
                tracked_shapes[matched_key]['smoother'].update(radius)
            else:
                shape_id_counter += 1; new_key = f"circle_{shape_id_counter}"
                tracked_shapes[new_key] = {'type':'circle', 'pos':(cx,cy), 'last_seen':frame_count, 'smoother':ValueSmoother(SHAPE_SMOOTHING_ALPHA)}
                tracked_shapes[new_key]['smoother'].update(radius)

        for r in detected_rects_inside_roi:
            # ... (内部矩形跟踪逻辑) ...
            if r == outer_roi_rect or r == inner_roi_rect: continue
            if r.w() * r.h() < MIN_SHAPE_AREA: continue
            cx, cy = r.x() + r.w() // 2, r.y() + r.h() // 2
            matched_key = None; min_dist = POSITION_TOLERANCE
            for key, data in tracked_shapes.items():
                if data['type'] == 'rect' and get_distance((cx, cy), data['pos']) < min_dist:
                    min_dist, matched_key = get_distance((cx, cy), data['pos']), key
            if matched_key:
                tracked_shapes[matched_key].update({'pos':(cx,cy), 'last_seen':frame_count})
                for i in range(4): tracked_shapes[matched_key]['smoother'][i].update(r.corners()[i])
            else:
                shape_id_counter += 1; new_key = f"rect_{shape_id_counter}"
                tracked_shapes[new_key] = {'type':'rect', 'pos':(cx,cy), 'last_seen':frame_count, 'smoother':[ValueSmoother(SHAPE_SMOOTHING_ALPHA) for _ in range(4)]}
                for i in range(4): tracked_shapes[new_key]['smoother'][i].update(r.corners()[i])
    else:
        tracked_shapes.clear()

    gc.collect()

    # ==================== 步骤 3: 绘制UI和精确距离 ====================
    dist_mm = 0
    if active_roi_tuple:
        frames_since_detection = 0

        # 1. (核心改进) 使用新的函数计算平均宽度
        raw_pixel_width = calculate_average_width(outer_roi_rect)
        # 2. (核心改进) 使用平滑器更新这个更准确的宽度值
        smoothed_pixel_width = width_smoother.update(raw_pixel_width)
        # 3. (核心改进) 使用【平滑后的平均宽度】来计算距离
        if smoothed_pixel_width > 0:
            dist_mm = (A4_REAL_W * FOCAL_PIX) / smoothed_pixel_width

        # --- 绘制 ---
        # (核心改进) 绘制精确的四边形轮廓
        for i in range(4):
            p1 = outer_roi_rect.corners()[i]
            p2 = outer_roi_rect.corners()[(i + 1) % 4]
            img.draw_line(p1[0], p1[1], p2[0], p2[1], color=(0, 255, 0), thickness=2)

        img.draw_rectangle(inner_roi_rect.rect(), color=(0, 255, 255), thickness=1)
        img.draw_string(5, 5, "Dist:%.1f cm" % (dist_mm / 10), color=(0, 255, 0), scale=2)
        img.draw_string(5, 50, "ROI: ACTIVE", color=(0, 255, 0), scale=1.5)
    else:
        # (核心改进) 如果丢失目标，显示上一次的稳定距离
        frames_since_detection += 1
        if frames_since_detection > DETECTION_TIMEOUT:
            width_smoother.reset()

        smoothed_width = width_smoother.get()
        if smoothed_width > 0:
            last_dist_mm = (A4_REAL_W * FOCAL_PIX) / smoothed_width
            img.draw_string(5, 5, "Dist: %.1f cm (Searching...)" % (last_dist_mm / 10), color=(255, 165, 0), scale=2)
        else:
            img.draw_string(5, 5, "Dist: N/A", color=(255, 255, 0), scale=2)
        img.draw_string(5, 50, "ROI: NOT FOUND", color=(255, 0, 0), scale=1.5)

    # --- 绘制内部形状 (逻辑保持不变) ---
    rect_count, circle_count = 0, 0
    for key, data in tracked_shapes.items():
        if data['type'] == 'circle':
            # ... (绘制内部圆形) ...
            circle_count += 1
            scx, scy = data['pos']; s_radius = data['smoother'].get()
            img.draw_circle(scx, scy, s_radius, color=(0, 0, 255), thickness=2)
            if SHOW_RADIUS: img.draw_string(scx + 5, scy, f"R:{s_radius}", color=(255, 165, 0))
            if SHOW_AREA: img.draw_string(scx + 5, scy + 15, f"A:{int(math.pi * s_radius**2)}", color=(255, 165, 0))
        elif data['type'] == 'rect':
            # ... (绘制内部矩形) ...
            rect_count += 1
            smoothed_corners = [s.get() for s in data['smoother']]
            for i in range(4):
                p1, p2 = smoothed_corners[i], smoothed_corners[(i + 1) % 4]
                img.draw_line(p1[0], p1[1], p2[0], p2[1], color=(0, 255, 0), thickness=2)
                if SHOW_EDGE_LENGTH:
                    edge_len = int(get_distance(p1, p2))
                    mid_x, mid_y = (p1[0] + p2[0]) // 2, (p1[1] + p2[1]) // 2
                    img.draw_string(mid_x, mid_y, str(edge_len), color=(255, 165, 0))

    # --- 清理和统计 (逻辑保持不变) ---
    keys_to_remove = [key for key, data in tracked_shapes.items() if frame_count - data['last_seen'] > SHAPE_TIMEOUT]
    for key in keys_to_remove: del tracked_shapes[key]
    stats_text = f"Rects:{rect_count} Circles:{circle_count} Tris:0"
    img.draw_string(5, 25, stats_text, color=(255, 255, 255), scale=2)

    # --- 显示图像 ---
    Display.show_image(img, x=(LCD_W - img.width()) // 2, y=(LCD_H - img.height()) // 2)
    gc.collect()
