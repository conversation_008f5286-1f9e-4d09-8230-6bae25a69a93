#
# Copyright (c) 2023 Canaan Inc.
#
# SPDX-License-Identifier: Apache-2.0
#

import time
from media.sensor import *
from media.display import *
from media.media import *
import math
import gc  # <--- 新增: 导入垃圾回收模块

# ==============================================================================
# ---------- 1. 全局参数配置 ----------
# ==============================================================================

# -- 显示与相机 --
LCD_W = 800
LCD_H = 480
SENSOR_W = 640
SENSOR_H = 480

# -- A4纸测距与ROI定位参数 --
A4_REAL_W = 210.0  # A4纸的真实宽度(毫米)
FOCAL_PIX = 670  # 预先标定好的相机像素焦距
ROI_FIND_THRESHOLD = 10000 # 用于寻找A4纸ROI的阈值

# -- ROI内部形状识别参数 --
SHAPE_FIND_THRESHOLD = 8000 # 用于在ROI内部寻找矩形的阈值
CIRCLE_FIND_THRESHOLD = 3000 # 用于在ROI内部寻找圆形的阈值
MIN_SHAPE_AREA = 50       # ROI内部形状的最小面积

# -- 形状跟踪与平滑参数 --
SMOOTHING_ALPHA = 0.6     # 平滑因子(0-1), 越大越接近当前值
POSITION_TOLERANCE = 25   # 判定为同一个形状的位置容差（像素）
SHAPE_TIMEOUT = 15        # 形状在多少帧未检测到后被清理

# -- 显示控制 --
SHOW_AREA = True
SHOW_EDGE_LENGTH = True
SHOW_RADIUS = True

# ==============================================================================
# ---------- 2. 辅助类与函数 ----------
# ==============================================================================

class ValueSmoother:
    def __init__(self, alpha=0.5):
        self.alpha = alpha
        self.smoothed_value = None
    def update(self, measurement):
        if self.smoothed_value is None: self.smoothed_value = measurement
        else: self.smoothed_value = self.alpha * measurement + (1 - self.alpha) * self.smoothed_value
        return self.get()
    def get(self):
        # 支持元组(tuple)和单个值的平滑
        if isinstance(self.smoothed_value, tuple):
            return tuple(int(v) for v in self.smoothed_value)
        return int(self.smoothed_value) if self.smoothed_value is not None else 0

def get_distance(p1, p2):
    return math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

def is_a4_like(rect):
    w, h = rect.w(), rect.h()
    if min(w, h) == 0: return False # 避免除以零错误
    ratio = max(w, h) / min(w, h)
    return 1.25 < ratio < 1.55

def inside(inner, outer):
    xo, yo, wo, ho = outer.rect()
    for (x, y) in inner.corners():
        if not (xo < x < xo + wo and yo < y < yo + ho): return False
    return True

# ==============================================================================
# ---------- 3. 硬件与变量初始化 ----------
# ==============================================================================

sensor = Sensor(width=640, height=480)
sensor.reset()
sensor.set_framesize(width=SENSOR_W, height=SENSOR_H)
sensor.set_pixformat(Sensor.RGB565)

Display.init(Display.ST7701, width=LCD_W, height=LCD_H, to_ide=True)
MediaManager.init()
sensor.run()

clk = time.clock()
frame_count = 0
tracked_shapes = {}
shape_id_counter = 0

# ==============================================================================
# ---------- 4. 主循环 ----------
# ==============================================================================

while True:
    clk.tick()

    gc.collect() # <--- 新增: 在读取新图像前，清理一次内存

    img = sensor.snapshot()
    frame_count += 1

    active_roi_tuple = None
    outer_roi_rect = None
    inner_roi_rect = None
    dist_mm = 0

    # --------------------------------------------------------------------------
    # 步骤 1: 在整个图像中寻找A4纸作为我们的ROI
    # --------------------------------------------------------------------------
    all_rects_for_roi_finding = img.find_rects(threshold=ROI_FIND_THRESHOLD)
    all_rects_for_roi_finding = sorted(all_rects_for_roi_finding, key=lambda r: r.w() * r.h(), reverse=True)

    if len(all_rects_for_roi_finding) >= 2:
        candidates = [r for r in all_rects_for_roi_finding[:2] if is_a4_like(r)]
        if len(candidates) == 2 and inside(candidates[1], candidates[0]):
            outer_roi_rect = candidates[0]
            inner_roi_rect = candidates[1]
            active_roi_tuple = outer_roi_rect.rect()

    # ----------------------------------------------------------------------
    # 步骤 2: 根据是否找到ROI，进行后续检测
    # ----------------------------------------------------------------------
    if active_roi_tuple:
        # 在ROI内部进行检测
        detected_circles_inside_roi = img.find_circles(roi=active_roi_tuple, threshold=CIRCLE_FIND_THRESHOLD, r_min=5)
        detected_rects_inside_roi = img.find_rects(roi=active_roi_tuple, threshold=SHAPE_FIND_THRESHOLD)

        # --- 更新跟踪对象的逻辑 ---
        current_frame_detected_keys = set()
        # 更新圆形
        for c in detected_circles_inside_roi:
            if math.pi * c.r()**2 < MIN_SHAPE_AREA: continue
            cx, cy, radius = c.x(), c.y(), c.r()
            matched_key = None; min_dist = POSITION_TOLERANCE
            for key, data in tracked_shapes.items():
                if data['type'] == 'circle' and get_distance((cx, cy), data['pos']) < min_dist:
                    min_dist, matched_key = get_distance((cx, cy), data['pos']), key
            if matched_key:
                tracked_shapes[matched_key].update({'pos':(cx,cy), 'last_seen':frame_count})
                tracked_shapes[matched_key]['smoother'].update(radius)
                current_frame_detected_keys.add(matched_key)
            else:
                shape_id_counter += 1; new_key = f"circle_{shape_id_counter}"
                tracked_shapes[new_key] = {'type':'circle', 'pos':(cx,cy), 'last_seen':frame_count, 'smoother':ValueSmoother(SMOOTHING_ALPHA)}
                tracked_shapes[new_key]['smoother'].update(radius)
                current_frame_detected_keys.add(new_key)

        # 更新矩形
        for r in detected_rects_inside_roi:
            if r == outer_roi_rect or r == inner_roi_rect: continue
            if r.w() * r.h() < MIN_SHAPE_AREA: continue
            cx, cy = r.x() + r.w() // 2, r.y() + r.h() // 2
            matched_key = None; min_dist = POSITION_TOLERANCE
            for key, data in tracked_shapes.items():
                if data['type'] == 'rect' and get_distance((cx, cy), data['pos']) < min_dist:
                    min_dist, matched_key = get_distance((cx, cy), data['pos']), key
            if matched_key:
                tracked_shapes[matched_key].update({'pos':(cx,cy), 'last_seen':frame_count})
                for i in range(4): tracked_shapes[matched_key]['smoother'][i].update(r.corners()[i])
                current_frame_detected_keys.add(matched_key)
            else:
                shape_id_counter += 1; new_key = f"rect_{shape_id_counter}"
                tracked_shapes[new_key] = {'type':'rect', 'pos':(cx,cy), 'last_seen':frame_count, 'smoother':[ValueSmoother(SMOOTHING_ALPHA) for _ in range(4)]}
                for i in range(4): tracked_shapes[new_key]['smoother'][i].update(r.corners()[i])
                current_frame_detected_keys.add(new_key)
    else:
        # 如果没有找到ROI，则清除所有跟踪对象
        tracked_shapes.clear()

    gc.collect() # <--- 新增: 在所有检测完成后，清理一次内存

    # --------------------------------------------------------------------------
    # 步骤 3: 绘制所有跟踪到的形状和UI
    # --------------------------------------------------------------------------
    if active_roi_tuple:
        img.draw_rectangle(outer_roi_rect.rect(), color=(255, 0, 0), thickness=2)
        img.draw_rectangle(inner_roi_rect.rect(), color=(0, 255, 255), thickness=2)
        pixel_width = outer_roi_rect.w()
        dist_mm = (A4_REAL_W * FOCAL_PIX) / pixel_width
        img.draw_string(5, 5, "Dist:%.1f cm" % (dist_mm / 10), color=(255, 255, 0), scale=2)
        img.draw_string(5, 50, "ROI: ACTIVE", color=(0, 255, 0), scale=1.5)
    else:
        img.draw_string(5, 5, "Dist: N/A", color=(255, 255, 0), scale=2)
        img.draw_string(5, 50, "ROI: NOT FOUND", color=(255, 0, 0), scale=1.5)

    rect_count = 0; circle_count = 0
    for key, data in tracked_shapes.items():
        if data['type'] == 'circle':
            circle_count += 1
            scx, scy = data['pos']
            s_radius = data['smoother'].get()
            img.draw_circle(scx, scy, s_radius, color=(0, 0, 255), thickness=2)
            if SHOW_RADIUS: img.draw_string(scx + 5, scy, f"R:{s_radius}", color=(255, 165, 0))
            if SHOW_AREA: img.draw_string(scx + 5, scy + 15, f"A:{int(math.pi * s_radius**2)}", color=(255, 165, 0))
        elif data['type'] == 'rect':
            rect_count += 1
            smoothed_corners = [smoother.get() for smoother in data['smoother']]
            for i in range(4):
                p1, p2 = smoothed_corners[i], smoothed_corners[(i + 1) % 4]
                img.draw_line(p1[0], p1[1], p2[0], p2[1], color=(0, 255, 0), thickness=2)
                if SHOW_EDGE_LENGTH:
                    edge_len = int(get_distance(p1, p2))
                    mid_x, mid_y = (p1[0] + p2[0]) // 2, (p1[1] + p2[1]) // 2
                    img.draw_string(mid_x, mid_y, str(edge_len), color=(255, 165, 0))

    # --------------------------------------------------------------------------
    # 步骤 4: 清理超时形状 & 显示统计信息
    # --------------------------------------------------------------------------
    keys_to_remove = [key for key, data in tracked_shapes.items() if frame_count - data['last_seen'] > SHAPE_TIMEOUT]
    for key in keys_to_remove:
        del tracked_shapes[key]

    stats_text = f"Rects:{rect_count} Circles:{circle_count} Tris:0"
    img.draw_string(5, 25, stats_text, color=(255, 255, 255), scale=2)

    # --------------------------------------------------------------------------
    # 步骤 5: 显示最终图像
    # --------------------------------------------------------------------------
    Display.show_image(img, x=(LCD_W - img.width()) // 2, y=(LCD_H - img.height()) // 2)

    gc.collect() # <--- 新增: 在循环末尾，清理一次内存
