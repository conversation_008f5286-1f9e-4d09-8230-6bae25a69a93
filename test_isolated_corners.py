#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
孤立拐点功能测试脚本
测试新增的孤立拐点检测功能
"""

import numpy as np
import sys
import os

# 添加当前目录到Python路径，以便导入主程序模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_isolated_corner_detection():
    """测试孤立拐点检测功能"""
    print("=" * 60)
    print("孤立拐点检测功能测试")
    print("=" * 60)
    
    # 导入主程序中的函数
    try:
        from 初始代码 import process_polygon_corners_and_find_min_edge
        print("✓ 成功导入主程序函数")
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    
    # 测试用例1：创建一个包含孤立拐点的多边形
    print("\n测试用例1: 包含孤立拐点的多边形")
    print("-" * 40)
    
    # 构造一个测试多边形：7个顶点，其中第3个顶点应该被识别为孤立拐点
    # 顶点排列：有效-无效-孤立-无效-有效-有效-无效
    test_polygon = np.array([
        [[100, 100]],  # 顶点0: 应该是有效拐点
        [[150, 120]],  # 顶点1: 应该是无效拐点
        [[200, 100]],  # 顶点2: 应该是孤立拐点（前后都是无效）
        [[250, 120]],  # 顶点3: 应该是无效拐点
        [[300, 100]],  # 顶点4: 应该是有效拐点
        [[350, 150]],  # 顶点5: 应该是有效拐点
        [[120, 150]]   # 顶点6: 应该是无效拐点
    ], dtype=np.int32)
    
    # 设置测试参数
    reference_pixels = 100.0  # 参考像素长度
    reference_physical_size = 10.0  # 参考物理长度（毫米）
    overlap_threshold = 0.3  # 较低的阈值以便测试
    
    print(f"测试多边形顶点数: {len(test_polygon)}")
    print(f"参考像素长度: {reference_pixels}")
    print(f"参考物理长度: {reference_physical_size} mm")
    print(f"重叠阈值: {overlap_threshold}")
    
    # 测试启用孤立拐点检测
    print("\n--- 启用孤立拐点检测 ---")
    try:
        min_edge_pixels, min_edge_physical, corner_marks, min_edge_indices, inner_points = process_polygon_corners_and_find_min_edge(
            test_polygon, reference_pixels, reference_physical_size,
            overlap_threshold, enable_isolated_detection=True, inner_contours=[]
        )
        
        print(f"最小边长: {min_edge_pixels:.2f} 像素 = {min_edge_physical:.2f} mm")
        print(f"拐点标记: {corner_marks}")
        print(f"最小边索引: {min_edge_indices}")
        
        # 分析结果
        valid_count = sum(1 for mark in corner_marks if mark == 1)
        isolated_count = sum(1 for mark in corner_marks if mark == 2)
        invalid_count = sum(1 for mark in corner_marks if mark == 0)
        
        print(f"拐点统计:")
        print(f"  - 普通有效拐点 (标记1): {valid_count} 个")
        print(f"  - 孤立拐点 (标记2): {isolated_count} 个")
        print(f"  - 无效拐点 (标记0): {invalid_count} 个")
        
        # 验证是否检测到孤立拐点
        if isolated_count > 0:
            print("✓ 成功检测到孤立拐点")
            isolated_indices = [i for i, mark in enumerate(corner_marks) if mark == 2]
            print(f"  孤立拐点位置: {isolated_indices}")
        else:
            print("⚠ 未检测到孤立拐点")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False
    
    # 测试禁用孤立拐点检测
    print("\n--- 禁用孤立拐点检测 ---")
    try:
        min_edge_pixels_2, min_edge_physical_2, corner_marks_2, min_edge_indices_2, inner_points_2 = process_polygon_corners_and_find_min_edge(
            test_polygon, reference_pixels, reference_physical_size,
            overlap_threshold, enable_isolated_detection=False, inner_contours=[]
        )
        
        print(f"最小边长: {min_edge_pixels_2:.2f} 像素 = {min_edge_physical_2:.2f} mm")
        print(f"拐点标记: {corner_marks_2}")
        print(f"最小边索引: {min_edge_indices_2}")
        
        # 分析结果
        valid_count_2 = sum(1 for mark in corner_marks_2 if mark == 1)
        isolated_count_2 = sum(1 for mark in corner_marks_2 if mark == 2)
        invalid_count_2 = sum(1 for mark in corner_marks_2 if mark == 0)
        
        print(f"拐点统计:")
        print(f"  - 普通有效拐点 (标记1): {valid_count_2} 个")
        print(f"  - 孤立拐点 (标记2): {isolated_count_2} 个")
        print(f"  - 无效拐点 (标记0): {invalid_count_2} 个")
        
        # 验证禁用功能是否生效
        if isolated_count_2 == 0:
            print("✓ 孤立拐点检测已正确禁用")
        else:
            print("✗ 孤立拐点检测禁用失败")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False
    
    # 比较两种模式的结果
    print("\n--- 结果比较 ---")
    print(f"启用孤立检测时的标记: {corner_marks}")
    print(f"禁用孤立检测时的标记: {corner_marks_2}")
    
    # 验证功能是否按预期工作
    differences = sum(1 for i in range(len(corner_marks)) if corner_marks[i] != corner_marks_2[i])
    if differences > 0:
        print(f"✓ 检测到 {differences} 个拐点标记差异，功能正常")
    else:
        print("⚠ 两种模式结果相同，可能需要调整测试用例")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    return True

def display_corner_types_info():
    """显示拐点类型说明"""
    print("\n" + "=" * 60)
    print("拐点分类说明")
    print("=" * 60)
    print("代码中现在支持三种拐点类型：")
    print()
    print("🔴 标记为 0 - 无效拐点")
    print("   - 特征：重叠度 < 阈值")
    print("   - 颜色：红色圆圈")
    print("   - 作用：不参与最短边长计算")
    print("   - 含义：可能是噪声或不重要的轮廓点")
    print()
    print("🟢 标记为 1 - 普通有效拐点")
    print("   - 特征：重叠度 >= 阈值，且不是孤立的")
    print("   - 颜色：绿色圆圈")
    print("   - 作用：参与最短边长计算")
    print("   - 含义：真实的几何拐点")
    print()
    print("🔵 标记为 2 - 孤立拐点（新增）")
    print("   - 特征：重叠度 >= 阈值，但前后相邻拐点都无效")
    print("   - 颜色：蓝色圆圈（稍大）")
    print("   - 作用：参与最短边长计算")
    print("   - 含义：独立的有效拐点，可能代表特殊的几何特征")
    print()
    print("配置参数：")
    print(f"  - enable_isolated_corner_detection = True  # 启用孤立拐点检测")
    print(f"  - polygon_edge_overlap_threshold = 0.5     # 重叠阈值")
    print("=" * 60)

if __name__ == "__main__":
    # 显示拐点类型说明
    display_corner_types_info()
    
    # 运行测试
    success = test_isolated_corner_detection()
    
    if success:
        print("\n🎉 所有测试通过！孤立拐点功能已成功添加。")
    else:
        print("\n❌ 测试失败，请检查代码实现。")
