#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试内点查找逻辑
"""

import numpy as np
import cv2

def calculate_distance(point1, point2):
    """计算两点间的欧几里得距离"""
    return ((point1[0] - point2[0]) ** 2 + (point1[1] - point2[1]) ** 2) ** 0.5

def find_closest_point_on_inner_contours_debug(target_point, inner_contours):
    """
    调试版本的内点查找函数
    """
    print(f"调试：目标点 {target_point}")
    print(f"调试：内轮廓数量 {len(inner_contours) if inner_contours else 0}")
    
    if not inner_contours:
        print(f"调试：没有内轮廓数据")
        return None

    min_distance = float('inf')
    closest_point = None

    try:
        for contour_idx, contour in enumerate(inner_contours):
            print(f"调试：处理内轮廓 {contour_idx}, 点数: {len(contour)}")
            
            # 显示轮廓的前几个点
            for i in range(min(5, len(contour))):
                point = contour[i]
                if len(point.shape) == 2 and point.shape[1] == 2:
                    px, py = point[0]
                    print(f"  轮廓{contour_idx}点{i}: ({px}, {py})")
            
            for point_idx, point in enumerate(contour):
                # 处理OpenCV轮廓点格式
                if len(point.shape) == 2 and point.shape[1] == 2:
                    # 格式: [[x, y]]
                    px, py = point[0]
                elif len(point.shape) == 1 and len(point) == 2:
                    # 格式: [x, y]
                    px, py = point
                else:
                    continue

                distance = calculate_distance(target_point, (px, py))
                if distance < min_distance:
                    min_distance = distance
                    closest_point = (int(px), int(py))
                    print(f"调试：找到更近点 ({px}, {py}), 距离: {distance:.2f}")

    except Exception as e:
        print(f"查找最近点时出错: {e}")

    print(f"调试：最终选择的内点: {closest_point}, 最小距离: {min_distance:.2f}")
    return closest_point

def test_inner_point_logic():
    """测试内点查找逻辑"""
    print("=" * 60)
    print("🔍 内点查找逻辑调试")
    print("=" * 60)
    
    # 根据您提供的数据
    target_point = (484, 184)  # 孤立拐点
    
    print(f"\n目标点（孤立拐点）: {target_point}")
    
    # 模拟一些内轮廓数据
    # 创建一些在目标点附近的轮廓
    inner_contours = []
    
    # 轮廓1：在目标点左侧
    contour1 = np.array([
        [[450, 180]], [[455, 185]], [[460, 190]], [[465, 185]], [[470, 180]]
    ])
    inner_contours.append(contour1)
    
    # 轮廓2：在目标点右侧
    contour2 = np.array([
        [[500, 180]], [[505, 185]], [[510, 190]], [[515, 185]], [[520, 180]]
    ])
    inner_contours.append(contour2)
    
    # 轮廓3：距离很远的轮廓（模拟错误的内点）
    contour3 = np.array([
        [[270, 175]], [[275, 180]], [[280, 185]], [[275, 190]], [[270, 185]]
    ])
    inner_contours.append(contour3)
    
    print(f"\n模拟内轮廓数据:")
    for i, contour in enumerate(inner_contours):
        print(f"轮廓 {i}: {[tuple(pt[0]) for pt in contour]}")
    
    # 测试查找逻辑
    result = find_closest_point_on_inner_contours_debug(target_point, inner_contours)
    
    print(f"\n🎯 结果分析:")
    print(f"找到的内点: {result}")
    
    if result:
        distance = calculate_distance(target_point, result)
        print(f"到目标点的距离: {distance:.2f}")
        
        # 分析是否合理
        if distance < 50:
            print("✅ 距离合理（< 50像素）")
        elif distance < 100:
            print("⚠️ 距离较远（50-100像素）")
        else:
            print("❌ 距离过远（> 100像素），可能有问题")

def analyze_problem():
    """分析问题原因"""
    print("\n" + "=" * 60)
    print("🔍 问题分析")
    print("=" * 60)
    
    print("\n📊 您的数据分析:")
    print("- 孤立拐点: (484, 184)")
    print("- 找到的内点: (274, 180)")
    print("- 距离差: 484 - 274 = 210 像素")
    print("- Y坐标差: 184 - 180 = 4 像素")
    
    print("\n🤔 可能的问题:")
    print("1. 内轮廓数据收集错误")
    print("   - 可能收集了错误的轮廓作为内轮廓")
    print("   - hierarchy 判断逻辑可能有误")
    
    print("2. 坐标系转换问题")
    print("   - ROI 模式下坐标转换错误")
    print("   - 内轮廓坐标没有正确转换")
    
    print("3. 轮廓格式解析错误")
    print("   - OpenCV 轮廓点格式解析有误")
    print("   - 数组维度处理错误")
    
    print("\n🔧 建议的调试步骤:")
    print("1. 检查内轮廓收集的 hierarchy 逻辑")
    print("2. 验证内轮廓坐标是否在合理范围内")
    print("3. 添加更多调试输出查看实际数据")
    print("4. 检查是否有坐标系转换问题")

if __name__ == "__main__":
    test_inner_point_logic()
    analyze_problem()
    
    print("\n" + "=" * 60)
    print("💡 下一步行动:")
    print("1. 运行实际程序查看调试输出")
    print("2. 检查内轮廓数据是否正确")
    print("3. 验证坐标转换逻辑")
    print("=" * 60)
