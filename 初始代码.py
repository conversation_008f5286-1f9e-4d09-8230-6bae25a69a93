from maix import image, camera, display, app, time
import cv2
import numpy as np

# 常量定义

# ==================== 辅助函数 ====================

def analyze_contour_hierarchy(contours, hierarchy):
    """
    分析轮廓层次结构，识别外轮廓和内轮廓的关系

    参数:
    - contours: 轮廓列表
    - hierarchy: 层次结构数组

    返回:
    - contour_info: 字典，包含每个轮廓的层次信息
    """
    contour_info = {}

    if hierarchy is None:
        return contour_info

    for i, contour in enumerate(contours):
        # hierarchy[0][i] = [next, previous, first_child, parent]
        next_contour = hierarchy[0][i][0]
        prev_contour = hierarchy[0][i][1]
        first_child = hierarchy[0][i][2]
        parent = hierarchy[0][i][3]

        contour_info[i] = {
            'contour': contour,
            'next': next_contour if next_contour != -1 else None,
            'previous': prev_contour if prev_contour != -1 else None,
            'first_child': first_child if first_child != -1 else None,
            'parent': parent if parent != -1 else None,
            'is_outer': parent == -1,  # 外轮廓没有父轮廓
            'children': []  # 子轮廓列表
        }

    # 建立父子关系
    for i, info in contour_info.items():
        if info['parent'] is not None:
            parent_idx = info['parent']
            if parent_idx in contour_info:
                contour_info[parent_idx]['children'].append(i)

    return contour_info

def get_polygon_with_holes(outer_contour_idx, contour_info, min_hole_area=50):
    """
    获取带孔洞的多边形信息

    参数:
    - outer_contour_idx: 外轮廓索引
    - contour_info: 轮廓层次信息
    - min_hole_area: 最小孔洞面积阈值

    返回:
    - polygon_data: 包含外轮廓和内轮廓的字典
    """
    if outer_contour_idx not in contour_info:
        return None

    outer_info = contour_info[outer_contour_idx]
    if not outer_info['is_outer']:
        return None

    # 获取外轮廓
    outer_contour = outer_info['contour']
    outer_area = cv2.contourArea(outer_contour)

    # 获取内轮廓（孔洞）
    holes = []
    for child_idx in outer_info['children']:
        if child_idx in contour_info:
            hole_contour = contour_info[child_idx]['contour']
            hole_area = cv2.contourArea(hole_contour)

            # 过滤太小的孔洞（可能是噪声）
            if hole_area >= min_hole_area:
                holes.append({
                    'contour': hole_contour,
                    'area': hole_area,
                    'index': child_idx
                })

    return {
        'outer_contour': outer_contour,
        'outer_area': outer_area,
        'outer_index': outer_contour_idx,
        'holes': holes,
        'has_holes': len(holes) > 0,
        'effective_area': outer_area - sum(hole['area'] for hole in holes)
    }

def detect_polygons_with_holes(img_cv, min_area=100, min_hole_area=50):
    """
    检测带孔洞的多边形

    参数:
    - img_cv: OpenCV格式的图像
    - min_area: 最小多边形面积
    - min_hole_area: 最小孔洞面积

    返回:
    - polygons_with_holes: 带孔洞的多边形列表
    """
    # 转换为灰度图
    if len(img_cv.shape) == 3:
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    else:
        gray = img_cv.copy()

    # 边缘检测
    edges = cv2.Canny(gray, 50, 150)

    # 形态学操作，连接断开的边缘
    kernel = np.ones((3, 3), np.uint8)
    edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

    # 查找轮廓，使用RETR_TREE获取完整的层次结构
    contours, hierarchy = cv2.findContours(edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

    if len(contours) == 0:
        return []

    # 分析轮廓层次结构
    contour_info = analyze_contour_hierarchy(contours, hierarchy)

    polygons_with_holes = []

    # 遍历所有外轮廓
    for i, info in contour_info.items():
        if info['is_outer']:  # 只处理外轮廓
            # 检查面积是否足够大
            area = cv2.contourArea(info['contour'])
            if area < min_area:
                continue

            # 轮廓近似为多边形
            epsilon = 0.02 * cv2.arcLength(info['contour'], True)
            approx = cv2.approxPolyDP(info['contour'], epsilon, True)

            # 只处理多边形（顶点数 >= 3）
            if len(approx) >= 3:
                polygon_data = get_polygon_with_holes(i, contour_info, min_hole_area)
                if polygon_data:
                    # 对孔洞也进行多边形近似
                    for hole in polygon_data['holes']:
                        hole_epsilon = 0.02 * cv2.arcLength(hole['contour'], True)
                        hole['approx'] = cv2.approxPolyDP(hole['contour'], hole_epsilon, True)

                    polygon_data['outer_approx'] = approx
                    polygon_data['vertices_count'] = len(approx)
                    polygons_with_holes.append(polygon_data)

    return polygons_with_holes

def draw_polygon_with_holes(img, polygon_data, outer_color=(0, 255, 0), hole_color=(0, 0, 255), thickness=2):
    """
    绘制带孔洞的多边形

    参数:
    - img: 目标图像
    - polygon_data: 多边形数据（来自get_polygon_with_holes）
    - outer_color: 外轮廓颜色
    - hole_color: 内轮廓颜色
    - thickness: 线条粗细
    """
    # 绘制外轮廓
    cv2.drawContours(img, [polygon_data['outer_approx']], -1, outer_color, thickness)

    # 绘制内轮廓（孔洞）
    for hole in polygon_data['holes']:
        if 'approx' in hole:
            cv2.drawContours(img, [hole['approx']], -1, hole_color, thickness)

    # 添加文本信息
    if polygon_data['outer_approx'] is not None and len(polygon_data['outer_approx']) > 0:
        # 计算外轮廓中心点
        M = cv2.moments(polygon_data['outer_approx'])
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])

            # 显示信息
            info_text = f"Vertices:{polygon_data['vertices_count']}"
            if polygon_data['has_holes']:
                info_text += f" Holes:{len(polygon_data['holes'])}"

            cv2.putText(img, info_text, (cx-50, cy), cv2.FONT_HERSHEY_SIMPLEX, 0.5, outer_color, 1)

def analyze_polygon_with_holes_example(img_cv):
    """
    使用示例：分析带孔洞的多边形
    """
    # 检测带孔洞的多边形
    polygons = detect_polygons_with_holes(img_cv, min_area=500, min_hole_area=100)

    # 创建结果图像
    result_img = img_cv.copy()

    print(f"检测到 {len(polygons)} 个多边形")

    for i, polygon in enumerate(polygons):
        print(f"\n多边形 {i+1}:")
        print(f"  外轮廓顶点数: {polygon['vertices_count']}")
        print(f"  外轮廓面积: {polygon['outer_area']:.2f}")
        print(f"  孔洞数量: {len(polygon['holes'])}")
        print(f"  有效面积: {polygon['effective_area']:.2f}")

        if polygon['has_holes']:
            for j, hole in enumerate(polygon['holes']):
                print(f"    孔洞 {j+1}: 面积 {hole['area']:.2f}")

        # 绘制多边形
        draw_polygon_with_holes(result_img, polygon)

    return result_img, polygons

# ==================== 初始化相机和显示 ====================
cam = camera.Camera(1024, 768, image.Format.FMT_BGR888)
disp = display.Display()

# ==================== 主循环示例（带内轮廓检测） ====================
def main_loop_with_hole_detection():
    """
    主循环示例：检测带孔洞的多边形
    """
    frame_count = 0

    while not app.need_exit():
        # 读取图像
        img = cam.read()

        # 转换为OpenCV格式
        img_cv = image.image2cv(img, ensure_bgr=True, copy=False)

        # 检测带孔洞的多边形
        polygons_with_holes = detect_polygons_with_holes(img_cv, min_area=1000, min_hole_area=200)

        # 在图像上绘制结果
        for polygon in polygons_with_holes:
            # 绘制外轮廓（绿色）
            if polygon['outer_approx'] is not None:
                for i in range(len(polygon['outer_approx'])):
                    pt1 = tuple(polygon['outer_approx'][i][0])
                    pt2 = tuple(polygon['outer_approx'][(i+1) % len(polygon['outer_approx'])][0])
                    img.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], image.COLOR_GREEN, 2)

                # 计算中心点并显示信息
                M = cv2.moments(polygon['outer_approx'])
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])

                    # 显示多边形信息
                    info_text = f"V:{polygon['vertices_count']}"
                    if polygon['has_holes']:
                        info_text += f" H:{len(polygon['holes'])}"
                    img.draw_string(cx-30, cy-20, info_text, image.COLOR_GREEN)

                    # 显示面积信息
                    area_text = f"A:{polygon['effective_area']:.0f}"
                    img.draw_string(cx-30, cy, area_text, image.COLOR_GREEN)

            # 绘制内轮廓（红色）
            for hole in polygon['holes']:
                if 'approx' in hole and hole['approx'] is not None:
                    for i in range(len(hole['approx'])):
                        pt1 = tuple(hole['approx'][i][0])
                        pt2 = tuple(hole['approx'][(i+1) % len(hole['approx'])][0])
                        img.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], image.COLOR_RED, 2)

        # 显示帧数和检测到的多边形数量
        status_text = f"Frame:{frame_count} Polygons:{len(polygons_with_holes)}"
        img.draw_string(10, 10, status_text, image.COLOR_WHITE)

        # 显示图像
        disp.show(img)

        frame_count += 1
        time.sleep_ms(50)  # 控制帧率

# ==================== 辅助函数 ====================
def get_value_from_history(values, use_latest=True):
    """从历史记录中获取值，默认使用最新值"""
    if not values:
        return 0

    # 返回最新的值
    if use_latest or len(values) < 2:
        return values[-1]

    # 如果需要平滑值（备用），返回最近几个值的简单平均
    return int(sum(values[-3:]) / min(len(values), 3))

def calculate_area_ratio(area1, area2):
    """计算两个面积的比例，确保结果在0-1之间"""
    max_area = max(area1, area2)
    if max_area == 0:
        return 1.0  # 如果两个面积都为0，认为完全匹配
    return min(area1, area2) / max_area

def calculate_distance(point1, point2):
    """计算两点之间的欧几里得距离"""
    return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)

def is_duplicate_shape(cx, cy, area, detected_shapes, shape_vertices, duplicate_distance_threshold, duplicate_area_ratio):
    """检查是否是重复的形状"""
    for detected_cx, detected_cy, detected_area, detected_vertices in detected_shapes:
        # 如果类型相同
        if detected_vertices == shape_vertices:
            # 检查中心点距离和面积比例
            distance = calculate_distance((cx, cy), (detected_cx, detected_cy))
            area_ratio = calculate_area_ratio(area, detected_area)

            # 如果中心点距离很近且面积比例接近1，认为是重复
            if distance < duplicate_distance_threshold and area_ratio > duplicate_area_ratio:
                return True
    return False

def find_matching_shape_position(cx, cy, shape_type, shape_tracking_data, position_tolerance):
    """查找匹配的形状位置"""
    shape_position = None
    min_distance = float('inf')

    for pos in shape_tracking_data.keys():
        if pos[0] == shape_type:
            distance = calculate_distance((cx, cy), (pos[1], pos[2]))
            if distance < position_tolerance and distance < min_distance:
                min_distance = distance
                shape_position = pos

    return shape_position

def count_vertices_inside_circle(quad_approx, circle_cx, circle_cy, circle_radius):
    """计算四边形有多少个顶点在圆内"""
    vertices_inside = 0
    for vertex in quad_approx:
        # vertex是一个包含[x, y]的数组
        vertex_x, vertex_y = vertex[0][0], vertex[0][1]
        vertex_distance = calculate_distance((vertex_x, vertex_y), (circle_cx, circle_cy))
        if vertex_distance <= circle_radius:
            vertices_inside += 1
    return vertices_inside

def calculate_overlap_area(quad_approx, circle_cx, circle_cy, circle_radius):
    """
    计算四边形与圆形的重叠面积比例
    返回重叠面积占四边形面积的比例 (0.0 - 1.0)
    """
    try:
        # 计算四边形面积
        quad_area = cv2.contourArea(quad_approx)
        if quad_area <= 0:
            return 0.0

        # 创建一个足够大的画布来绘制形状
        canvas_size = max(800, int(circle_radius * 3))

        # 计算偏移量，将圆心放在画布中心
        offset_x = canvas_size // 2 - circle_cx
        offset_y = canvas_size // 2 - circle_cy

        # 调整四边形顶点坐标
        adjusted_quad = quad_approx.copy()
        for i in range(len(adjusted_quad)):
            adjusted_quad[i][0][0] += offset_x
            adjusted_quad[i][0][1] += offset_y

        # 调整圆心坐标
        adjusted_circle_cx = canvas_size // 2
        adjusted_circle_cy = canvas_size // 2

        # 绘制四边形
        quad_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
        # 转换轮廓格式：从 [[[x, y]], [[x, y]], ...] 到 [[x, y], [x, y], ...]
        adjusted_quad_points = adjusted_quad.reshape(-1, 2)
        cv2.fillPoly(quad_mask, [adjusted_quad_points], 255)

        # 绘制圆形
        circle_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
        cv2.circle(circle_mask, (adjusted_circle_cx, adjusted_circle_cy), circle_radius, 255, -1)

        # 计算重叠区域
        overlap_mask = cv2.bitwise_and(quad_mask, circle_mask)
        overlap_area = cv2.countNonZero(overlap_mask)

        # 计算重叠比例
        overlap_ratio = overlap_area / cv2.countNonZero(quad_mask) if cv2.countNonZero(quad_mask) > 0 else 0.0

        return min(overlap_ratio, 1.0)  # 确保不超过1.0

    except Exception as e:
        print(f"计算重叠面积时出错: {e}")
        return 0.0

def is_quad_inside_circle_hybrid(quad_approx, circle_cx, circle_cy, circle_radius,
                                min_vertices_conservative=2, min_vertices_aggressive=3):
    """
    混合策略判断四边形是否在圆内
    结合中心点检查和顶点检查

    参数:
    - min_vertices_conservative: 保守过滤时，中心在圆内需要的最少顶点数
    - min_vertices_aggressive: 积极过滤时，中心在圆内需要的最少顶点数
    """
    # 计算四边形中心点
    M = cv2.moments(quad_approx)
    if M["m00"] == 0:
        return False, "无法计算中心点"

    quad_cx = int(M["m10"] / M["m00"])
    quad_cy = int(M["m01"] / M["m00"])

    # 策略1: 检查中心点是否在圆内
    center_distance = calculate_distance((quad_cx, quad_cy), (circle_cx, circle_cy))
    center_inside = center_distance <= circle_radius

    # 策略2: 检查顶点在圆内的数量
    vertices_inside_count = count_vertices_inside_circle(quad_approx, circle_cx, circle_cy, circle_radius)

    # 混合判断逻辑
    if center_inside and vertices_inside_count >= min_vertices_aggressive:
        # 中心在圆内且满足积极过滤条件 -> 确定在圆内
        reason = f"中心在圆内(距离:{center_distance:.1f}<={circle_radius}) 且 {vertices_inside_count}/4 个顶点在圆内 (积极过滤)"
        return True, reason
    elif center_inside and vertices_inside_count >= min_vertices_conservative:
        # 中心在圆内且满足保守过滤条件 -> 可能在圆内
        reason = f"中心在圆内(距离:{center_distance:.1f}<={circle_radius}) 且 {vertices_inside_count}/4 个顶点在圆内 (保守过滤)"
        return True, reason
    elif vertices_inside_count == 4:
        # 所有顶点都在圆内 -> 确定在圆内（即使中心可能因为计算误差不在圆内）
        reason = f"所有4个顶点都在圆内 (中心距离:{center_distance:.1f})"
        return True, reason
    else:
        # 其他情况 -> 不在圆内
        reason = f"中心{'在圆内' if center_inside else '在圆外'}(距离:{center_distance:.1f}) 且仅 {vertices_inside_count}/4 个顶点在圆内"
        return False, reason

def is_quad_inside_circle_area_overlap(quad_approx, circle_cx, circle_cy, circle_radius, overlap_threshold=0.7):
    """
    基于面积重叠判断四边形是否在圆内

    参数:
    - overlap_threshold: 重叠面积比例阈值，超过此值认为四边形在圆内
    """
    overlap_ratio = calculate_overlap_area(quad_approx, circle_cx, circle_cy, circle_radius)

    if overlap_ratio >= overlap_threshold:
        reason = f"面积重叠比例: {overlap_ratio:.2f} >= {overlap_threshold} (面积重叠过滤)"
        return True, reason
    else:
        reason = f"面积重叠比例: {overlap_ratio:.2f} < {overlap_threshold}"
        return False, reason

def is_quad_inside_circle_comprehensive(quad_approx, circle_cx, circle_cy, circle_radius,
                                      min_vertices_conservative=2, min_vertices_aggressive=3,
                                      overlap_threshold=0.7):
    """
    综合策略判断四边形是否在圆内
    结合混合策略（中心点+顶点）和面积重叠检查

    优先级：
    1. 面积重叠检查（处理顶点都在圆外但面积高度重合的情况）
    2. 混合策略检查（中心点+顶点检查）
    """
    # 策略1: 面积重叠检查（优先级最高）
    area_inside, area_reason = is_quad_inside_circle_area_overlap(
        quad_approx, circle_cx, circle_cy, circle_radius, overlap_threshold
    )
    if area_inside:
        return True, area_reason

    # 策略2: 混合策略检查（中心点+顶点）
    hybrid_inside, hybrid_reason = is_quad_inside_circle_hybrid(
        quad_approx, circle_cx, circle_cy, circle_radius,
        min_vertices_conservative, min_vertices_aggressive
    )
    if hybrid_inside:
        return True, hybrid_reason

    # 都不满足条件，不在圆内
    return False, f"综合判断：{area_reason} 且 {hybrid_reason}"

def count_vertices_inside_polygon(quad_approx, polygon_approx):
    """计算四边形有多少个顶点在多边形内"""
    vertices_inside = 0
    try:
        print(f"    🔍 检查顶点: 四边形格式={quad_approx.shape}, 多边形格式={polygon_approx.shape}")
        for i, vertex in enumerate(quad_approx):
            # vertex是一个包含[x, y]的数组
            vertex_x, vertex_y = int(vertex[0][0]), int(vertex[0][1])
            # 使用OpenCV的pointPolygonTest检查点是否在多边形内
            # 确保坐标是整数类型
            result = cv2.pointPolygonTest(polygon_approx, (vertex_x, vertex_y), False)
            if result >= 0:
                vertices_inside += 1
                print(f"      顶点{i}({vertex_x}, {vertex_y}) 在多边形内, 结果={result}")
            else:
                print(f"      顶点{i}({vertex_x}, {vertex_y}) 在多边形外, 结果={result}")
    except Exception as e:
        print(f"    ❌ 顶点检查出错: {e}")
        print(f"    四边形类型: {type(quad_approx)}, 多边形类型: {type(polygon_approx)}")
        if hasattr(quad_approx, 'dtype'):
            print(f"    四边形dtype: {quad_approx.dtype}")
        if hasattr(polygon_approx, 'dtype'):
            print(f"    多边形dtype: {polygon_approx.dtype}")
        raise
    return vertices_inside

def calculate_polygon_overlap_area(quad_approx, polygon_approx):
    """
    计算四边形与多边形的重叠面积比例
    返回重叠面积占四边形面积的比例 (0.0 - 1.0)
    """
    try:
        # 计算四边形面积
        quad_area = cv2.contourArea(quad_approx)
        if quad_area <= 0:
            return 0.0

        # 计算所有点的边界框，确定偏移量
        # 安全地处理数组格式
        try:
            quad_points = quad_approx.reshape(-1, 2)
            polygon_points = polygon_approx.reshape(-1, 2)
            all_points = np.vstack([quad_points, polygon_points])
        except Exception as e:
            print(f"数组格式错误: {e}, quad_shape={quad_approx.shape}, polygon_shape={polygon_approx.shape}")
            return 0.0

        min_x, min_y = np.min(all_points, axis=0)
        max_x, max_y = np.max(all_points, axis=0)

        # 动态计算画布大小，确保足够大
        shape_width = max_x - min_x
        shape_height = max_y - min_y
        canvas_size = max(800, int(max(shape_width, shape_height) * 2 + 100))

        # 计算偏移量，将所有形状移到画布中心
        offset_x = canvas_size // 2 - (min_x + max_x) // 2
        offset_y = canvas_size // 2 - (min_y + max_y) // 2

        # 调整四边形顶点坐标
        adjusted_quad = quad_approx.copy()
        for i in range(len(adjusted_quad)):
            adjusted_quad[i][0][0] += offset_x
            adjusted_quad[i][0][1] += offset_y

        # 调整多边形顶点坐标
        adjusted_polygon = polygon_approx.copy()
        for i in range(len(adjusted_polygon)):
            adjusted_polygon[i][0][0] += offset_x
            adjusted_polygon[i][0][1] += offset_y

        # 绘制四边形
        quad_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
        adjusted_quad_points = adjusted_quad.reshape(-1, 2)
        cv2.fillPoly(quad_mask, [adjusted_quad_points], 255)

        # 绘制多边形
        polygon_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
        adjusted_polygon_points = adjusted_polygon.reshape(-1, 2)
        cv2.fillPoly(polygon_mask, [adjusted_polygon_points], 255)

        # 计算重叠区域
        overlap_mask = cv2.bitwise_and(quad_mask, polygon_mask)
        overlap_area = cv2.countNonZero(overlap_mask)

        # 计算重叠比例
        quad_pixels = cv2.countNonZero(quad_mask)
        overlap_ratio = overlap_area / quad_pixels if quad_pixels > 0 else 0.0

        # 添加调试信息
        print(f"  📐 多边形重叠计算: 重叠像素={overlap_area}, 四边形像素={quad_pixels}, 比例={overlap_ratio:.3f}")

        return min(overlap_ratio, 1.0)  # 确保不超过1.0

    except Exception as e:
        print(f"计算多边形重叠面积时出错: {e}")
        return 0.0

def is_quad_inside_polygon_hybrid(quad_approx, polygon_approx,
                                min_vertices_conservative=2, min_vertices_aggressive=3):
    """
    混合策略判断四边形是否在多边形内
    结合中心点检查和顶点检查

    参数:
    - min_vertices_conservative: 保守过滤时，中心在多边形内需要的最少顶点数
    - min_vertices_aggressive: 积极过滤时，中心在多边形内需要的最少顶点数
    """
    # 计算四边形中心点
    M = cv2.moments(quad_approx)
    if M["m00"] == 0:
        return False, "无法计算中心点"

    quad_cx = int(M["m10"] / M["m00"])
    quad_cy = int(M["m01"] / M["m00"])

    # 策略1: 检查中心点是否在多边形内
    center_inside = cv2.pointPolygonTest(polygon_approx, (quad_cx, quad_cy), False) >= 0

    # 策略2: 检查顶点在多边形内的数量
    vertices_inside_count = count_vertices_inside_polygon(quad_approx, polygon_approx)

    # 混合判断逻辑
    if center_inside and vertices_inside_count >= min_vertices_aggressive:
        # 中心在多边形内且满足积极过滤条件 -> 确定在多边形内
        reason = f"中心在多边形内 且 {vertices_inside_count}/4 个顶点在多边形内 (积极过滤)"
        return True, reason
    elif center_inside and vertices_inside_count >= min_vertices_conservative:
        # 中心在多边形内且满足保守过滤条件 -> 可能在多边形内
        reason = f"中心在多边形内 且 {vertices_inside_count}/4 个顶点在多边形内 (保守过滤)"
        return True, reason
    elif vertices_inside_count == 4:
        # 所有顶点都在多边形内 -> 确定在多边形内（即使中心可能因为计算误差不在多边形内）
        reason = f"所有4个顶点都在多边形内"
        return True, reason
    else:
        # 其他情况 -> 不在多边形内
        reason = f"中心{'在多边形内' if center_inside else '在多边形外'} 且仅 {vertices_inside_count}/4 个顶点在多边形内"
        return False, reason

def is_quad_inside_polygon_area_overlap(quad_approx, polygon_approx, overlap_threshold=0.7):
    """
    基于面积重叠判断四边形是否在多边形内

    参数:
    - overlap_threshold: 重叠面积比例阈值，超过此值认为四边形在多边形内
    """
    overlap_ratio = calculate_polygon_overlap_area(quad_approx, polygon_approx)

    if overlap_ratio >= overlap_threshold:
        reason = f"面积重叠比例: {overlap_ratio:.2f} >= {overlap_threshold} (面积重叠过滤)"
        return True, reason
    else:
        reason = f"面积重叠比例: {overlap_ratio:.2f} < {overlap_threshold}"
        return False, reason

def is_quad_inside_polygon_comprehensive(quad_approx, polygon_approx,
                                       min_vertices_conservative=2, min_vertices_aggressive=3,
                                       overlap_threshold=0.7):
    """
    综合策略判断四边形是否在多边形内
    结合混合策略（中心点+顶点）和面积重叠检查

    优先级：
    1. 面积重叠检查（处理顶点都在多边形外但面积高度重合的情况）
    2. 混合策略检查（中心点+顶点检查）
    """
    # 策略1: 面积重叠检查（优先级最高）
    area_inside, area_reason = is_quad_inside_polygon_area_overlap(
        quad_approx, polygon_approx, overlap_threshold
    )
    if area_inside:
        return True, area_reason

    # 策略2: 混合策略检查（中心点+顶点）
    hybrid_inside, hybrid_reason = is_quad_inside_polygon_hybrid(
        quad_approx, polygon_approx,
        min_vertices_conservative, min_vertices_aggressive
    )
    if hybrid_inside:
        return True, hybrid_reason

    # 都不满足条件，不在多边形内
    return False, f"综合判断：{area_reason} 且 {hybrid_reason}"

def count_vertices_inside_polygon_triangle(triangle_approx, polygon_approx):
    """计算三角形有多少个顶点在多边形内"""
    vertices_inside = 0
    try:
        print(f"    🔍 检查三角形顶点: 三角形格式={triangle_approx.shape}, 多边形格式={polygon_approx.shape}")
        for i, vertex in enumerate(triangle_approx):
            # vertex是一个包含[x, y]的数组
            vertex_x, vertex_y = int(vertex[0][0]), int(vertex[0][1])
            # 使用OpenCV的pointPolygonTest检查点是否在多边形内
            result = cv2.pointPolygonTest(polygon_approx, (vertex_x, vertex_y), False)
            if result >= 0:
                vertices_inside += 1
                print(f"      三角形顶点{i}({vertex_x}, {vertex_y}) 在多边形内, 结果={result}")
            else:
                print(f"      三角形顶点{i}({vertex_x}, {vertex_y}) 在多边形外, 结果={result}")
    except Exception as e:
        print(f"    ❌ 三角形顶点检查出错: {e}")
        print(f"    三角形类型: {type(triangle_approx)}, 多边形类型: {type(polygon_approx)}")
        if hasattr(triangle_approx, 'dtype'):
            print(f"    三角形dtype: {triangle_approx.dtype}")
        if hasattr(polygon_approx, 'dtype'):
            print(f"    多边形dtype: {polygon_approx.dtype}")
        raise
    return vertices_inside

def calculate_triangle_polygon_overlap_area(triangle_approx, polygon_approx):
    """
    计算三角形与多边形的重叠面积比例
    返回重叠面积占三角形面积的比例 (0.0 - 1.0)
    """
    try:
        # 计算三角形面积
        triangle_area = cv2.contourArea(triangle_approx)
        if triangle_area <= 0:
            return 0.0

        # 计算所有点的边界框，确定偏移量
        try:
            triangle_points = triangle_approx.reshape(-1, 2)
            polygon_points = polygon_approx.reshape(-1, 2)
            all_points = np.vstack([triangle_points, polygon_points])
        except Exception as e:
            print(f"数组格式错误: {e}, triangle_shape={triangle_approx.shape}, polygon_shape={polygon_approx.shape}")
            return 0.0

        min_x, min_y = np.min(all_points, axis=0)
        max_x, max_y = np.max(all_points, axis=0)

        # 动态计算画布大小，确保足够大
        shape_width = max_x - min_x
        shape_height = max_y - min_y
        canvas_size = max(800, int(max(shape_width, shape_height) * 2 + 100))

        # 计算偏移量，将所有形状移到画布中心
        offset_x = canvas_size // 2 - (min_x + max_x) // 2
        offset_y = canvas_size // 2 - (min_y + max_y) // 2

        # 调整三角形顶点坐标
        adjusted_triangle = triangle_approx.copy()
        for i in range(len(adjusted_triangle)):
            adjusted_triangle[i][0][0] += offset_x
            adjusted_triangle[i][0][1] += offset_y

        # 调整多边形顶点坐标
        adjusted_polygon = polygon_approx.copy()
        for i in range(len(adjusted_polygon)):
            adjusted_polygon[i][0][0] += offset_x
            adjusted_polygon[i][0][1] += offset_y

        # 绘制三角形
        triangle_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
        adjusted_triangle_points = adjusted_triangle.reshape(-1, 2)
        cv2.fillPoly(triangle_mask, [adjusted_triangle_points], 255)

        # 绘制多边形
        polygon_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
        adjusted_polygon_points = adjusted_polygon.reshape(-1, 2)
        cv2.fillPoly(polygon_mask, [adjusted_polygon_points], 255)

        # 计算重叠区域
        overlap_mask = cv2.bitwise_and(triangle_mask, polygon_mask)
        overlap_area = cv2.countNonZero(overlap_mask)

        # 计算重叠比例
        triangle_pixels = cv2.countNonZero(triangle_mask)
        overlap_ratio = overlap_area / triangle_pixels if triangle_pixels > 0 else 0.0

        # 添加调试信息
        print(f"  📐 三角形多边形重叠计算: 重叠像素={overlap_area}, 三角形像素={triangle_pixels}, 比例={overlap_ratio:.3f}")

        return min(overlap_ratio, 1.0)  # 确保不超过1.0

    except Exception as e:
        print(f"计算三角形多边形重叠面积时出错: {e}")
        return 0.0

def is_triangle_inside_polygon_hybrid(triangle_approx, polygon_approx,
                                    min_vertices_conservative=2, min_vertices_aggressive=3):
    """
    混合策略判断三角形是否在多边形内
    结合中心点检查和顶点检查

    参数:
    - min_vertices_conservative: 保守过滤时，中心在多边形内需要的最少顶点数
    - min_vertices_aggressive: 积极过滤时，中心在多边形内需要的最少顶点数
    """
    # 计算三角形中心点
    M = cv2.moments(triangle_approx)
    if M["m00"] == 0:
        return False, "无法计算中心点"

    triangle_cx = int(M["m10"] / M["m00"])
    triangle_cy = int(M["m01"] / M["m00"])

    # 策略1: 检查中心点是否在多边形内
    center_inside = cv2.pointPolygonTest(polygon_approx, (triangle_cx, triangle_cy), False) >= 0

    # 策略2: 检查顶点在多边形内的数量
    vertices_inside_count = count_vertices_inside_polygon_triangle(triangle_approx, polygon_approx)

    # 混合判断逻辑
    if center_inside and vertices_inside_count >= min_vertices_aggressive:
        # 中心在多边形内且满足积极过滤条件 -> 确定在多边形内
        reason = f"中心在多边形内 且 {vertices_inside_count}/3 个顶点在多边形内 (积极过滤)"
        return True, reason
    elif center_inside and vertices_inside_count >= min_vertices_conservative:
        # 中心在多边形内且满足保守过滤条件 -> 可能在多边形内
        reason = f"中心在多边形内 且 {vertices_inside_count}/3 个顶点在多边形内 (保守过滤)"
        return True, reason
    elif vertices_inside_count == 3:
        # 所有顶点都在多边形内 -> 确定在多边形内（即使中心可能因为计算误差不在多边形内）
        reason = f"所有3个顶点都在多边形内"
        return True, reason
    else:
        # 其他情况 -> 不在多边形内
        reason = f"中心{'在多边形内' if center_inside else '在多边形外'} 且仅 {vertices_inside_count}/3 个顶点在多边形内"
        return False, reason

def is_triangle_inside_polygon_area_overlap(triangle_approx, polygon_approx, overlap_threshold=0.7):
    """
    基于面积重叠判断三角形是否在多边形内

    参数:
    - overlap_threshold: 重叠面积比例阈值，超过此值认为三角形在多边形内
    """
    overlap_ratio = calculate_triangle_polygon_overlap_area(triangle_approx, polygon_approx)

    if overlap_ratio >= overlap_threshold:
        reason = f"面积重叠比例: {overlap_ratio:.2f} >= {overlap_threshold} (面积重叠过滤)"
        return True, reason
    else:
        reason = f"面积重叠比例: {overlap_ratio:.2f} < {overlap_threshold}"
        return False, reason

def is_triangle_inside_polygon_comprehensive(triangle_approx, polygon_approx,
                                           min_vertices_conservative=2, min_vertices_aggressive=3,
                                           overlap_threshold=0.7):
    """
    综合策略判断三角形是否在多边形内
    结合混合策略（中心点+顶点）和面积重叠检查

    优先级：
    1. 面积重叠检查（处理顶点都在多边形外但面积高度重合的情况）
    2. 混合策略检查（中心点+顶点检查）
    """
    # 策略1: 面积重叠检查（优先级最高）
    area_inside, area_reason = is_triangle_inside_polygon_area_overlap(
        triangle_approx, polygon_approx, overlap_threshold
    )
    if area_inside:
        return True, area_reason

    # 策略2: 混合策略检查（中心点+顶点）
    hybrid_inside, hybrid_reason = is_triangle_inside_polygon_hybrid(
        triangle_approx, polygon_approx,
        min_vertices_conservative, min_vertices_aggressive
    )
    if hybrid_inside:
        return True, hybrid_reason

    # 都不满足条件，不在多边形内
    return False, f"综合判断：{area_reason} 且 {hybrid_reason}"

def process_retained_quadrilateral(quad_approx, cx, cy, area, is_max_rect, frame_count,
                                 shape_tracking_data, vertex_history, vertex_history_size,
                                 position_tolerance, last_frame_shapes, img_result, edge_history,
                                 edge_history_size, use_instant_values, max_rectangles=None,
                                 enable_distance_measurement=False, calibration_pixels=12.0,
                                 calibration_distance=200.0, distance_history=None,
                                 distance_history_size=5, enable_inner_shape_stats=False,
                                 inner_shapes_stats=None, img_cv=None, edges=None, min_area=100,
                                 detected_shapes=None, duplicate_distance_threshold=15,
                                 duplicate_area_ratio=0.8, enable_preprocess=True,
                                 preprocess_started=False, preprocess_stable_frames=0,
                                 preprocess_stable_threshold=2):
    """
    处理保留的四边形（未被过滤的四边形）
    进行完整的跟踪、绘制和计算
    """
    global current_distance, second_largest_rect_info

    shape = "Quad"  # 四边形
    # 对最大的两个矩形使用特殊颜色
    color = image.COLOR_YELLOW if is_max_rect else image.COLOR_GREEN

    # 提取顶点坐标列表
    vertices = [tuple(pt[0]) for pt in quad_approx]

    # 查找匹配的形状位置并更新跟踪数据
    shape_position = find_matching_shape_position(cx, cy, "Quad", shape_tracking_data, position_tolerance)
    shape_position, cx, cy, vertices = update_shape_tracking(
        shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
        vertex_history, vertex_history_size, "Quad"
    )

    # 记录该四边形到当前帧四边形列表
    if "Quad" not in last_frame_shapes:
        last_frame_shapes["Quad"] = []
    last_frame_shapes["Quad"].append((cx, cy, area))

    # 更新框内图形统计数据（只统计非最大矩形的四边形，且只在预处理完成后统计）
    if (enable_inner_shape_stats and not is_max_rect and inner_shapes_stats is not None and
        enable_preprocess and len(max_rectangles) == 2 and
        frame_count >= preprocess_start_frame + preprocess_stable_threshold):
        update_inner_shape_stats("Quad", vertices=vertices, inner_shapes_stats=inner_shapes_stats,
                                max_rectangles=max_rectangles, second_largest_rect_physical_length=second_largest_rect_physical_length)

    # 检查是否为第二大矩形并进行距离测量
    is_second_largest = False
    if (enable_distance_measurement and max_rectangles and
        len(max_rectangles) >= 2 and distance_history is not None):

        is_second_largest = is_second_largest_rectangle(
            cx, cy, area, max_rectangles,
            duplicate_distance_threshold, duplicate_area_ratio
        )

        if is_second_largest:
            # 计算最长边
            longest_edge = calculate_longest_edge(vertices)

            if longest_edge > 0:
                # 计算距离
                calculated_distance = calculate_distance_from_pixels(
                    longest_edge, calibration_pixels, calibration_distance
                )

                # 添加到历史记录进行平滑
                distance_history.append(calculated_distance)
                if len(distance_history) > distance_history_size:
                    distance_history.pop(0)

                # 计算平滑后的距离
                current_distance = sum(distance_history) / len(distance_history)

                # 保存第二大矩形信息
                second_largest_rect_info = {
                    'center': (cx, cy),
                    'longest_edge': longest_edge,
                    'distance': current_distance,
                    'vertices': vertices
                }

                # 在控制台打印距离信息（简化输出）
                # 简化输出，不打印详细的距离测量信息

                # 在图像上显示距离信息
                distance_text = f"Dist:{current_distance:.1f}cm"
                img_result.draw_string(cx-30, cy-30, distance_text, image.COLOR_ORANGE)

                # 标记为第二大矩形
                shape += "(2nd)"
                color = image.COLOR_ORANGE

    # 在控制台打印形状信息（只在预处理完成后打印）
    if (enable_preprocess and len(max_rectangles) == 2 and 
        frame_count >= preprocess_start_frame + preprocess_stable_threshold):
        # 简化输出，不打印详细的检测信息
        pass

    # 在图像上标记识别结果
    img_result.draw_string(cx-20, cy, shape, color)
    # 如果启用，显示轮廓面积
    if show_shape_areas:
        area_text = f"A:{int(area)}"
        img_result.draw_string(cx-20, cy+15, area_text, color)

    # 画出轮廓和拐点，并显示边长
    draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                          edge_history, edge_history_size, use_instant_values)

    # 在四边形区域内检测多边形（只在预处理完成后且提供了必要参数时）
    if (img_cv is not None and edges is not None and detected_shapes is not None and
        enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
        preprocess_stable_frames >= preprocess_stable_threshold):

        detect_polygons_in_quad_region(
            quad_approx, img_cv, edges, min_area, detected_shapes,
            duplicate_distance_threshold, duplicate_area_ratio, img_result,
            frame_count, shape_tracking_data, vertex_history, vertex_history_size,
            position_tolerance, last_frame_shapes, enable_preprocess,
            preprocess_started, max_rectangles, preprocess_stable_frames,
            preprocess_stable_threshold, enable_inner_shape_stats, inner_shapes_stats,
            second_largest_rect_physical_length
        )

    return shape_position, cx, cy, vertices

def update_shape_tracking(shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
                         vertex_history, vertex_history_size, shape_type):
    """更新形状跟踪数据"""
    if shape_position is None:
        # 创建新的位置标识
        shape_position = (shape_type, cx, cy)
        shape_tracking_data[shape_position] = frame_count

        # 初始化顶点历史记录
        if isinstance(vertices, list):
            vertex_history[shape_position] = [vertices]
    else:
        # 更新现有形状
        shape_tracking_data[shape_position] = frame_count

        # 更新顶点历史记录
        if isinstance(vertices, list):
            if shape_position in vertex_history:
                vertex_history[shape_position].append(vertices)
                if len(vertex_history[shape_position]) > vertex_history_size:
                    vertex_history[shape_position].pop(0)
            else:
                vertex_history[shape_position] = [vertices]

    return shape_position, cx, cy, vertices

def calculate_longest_edge(vertices):
    """计算形状的最长边长度"""
    if len(vertices) < 2:
        return 0

    max_length = 0
    for i in range(len(vertices)):
        pt1 = vertices[i]
        pt2 = vertices[(i+1) % len(vertices)]
        edge_length = calculate_distance(pt1, pt2)
        if edge_length > max_length:
            max_length = edge_length

    return max_length

def calculate_distance_from_pixels(current_pixels, calibration_pixels, calibration_distance):
    """根据像素值计算实际距离"""
    if current_pixels <= 0:
        return 0.0

    # 使用反比例关系：d₂ = (calibration_pixels × calibration_distance) / current_pixels
    calculated_distance = (calibration_pixels * calibration_distance) / current_pixels
    return calculated_distance

def is_second_largest_rectangle(cx, cy, area, max_rectangles, duplicate_distance_threshold, duplicate_area_ratio):
    """判断当前四边形是否为第二大矩形"""
    if len(max_rectangles) < 2:
        return False

    # 获取第二大矩形（索引为1）
    _, second_max_area, second_max_approx = max_rectangles[1]

    # 计算第二大矩形的中心点
    M_second = cv2.moments(second_max_approx)
    if M_second["m00"] == 0:
        return False

    cx_second = int(M_second["m10"] / M_second["m00"])
    cy_second = int(M_second["m01"] / M_second["m00"])

    # 计算距离和面积比
    distance = calculate_distance((cx, cy), (cx_second, cy_second))
    area_ratio = calculate_area_ratio(area, second_max_area)

    # 判断是否是同一个矩形
    return distance < duplicate_distance_threshold and area_ratio > duplicate_area_ratio

def calculate_all_edge_lengths(vertices):
    """计算形状所有边的长度"""
    if len(vertices) < 2:
        return []

    edge_lengths = []
    for i in range(len(vertices)):
        pt1 = vertices[i]
        pt2 = vertices[(i+1) % len(vertices)]
        edge_length = calculate_distance(pt1, pt2)
        edge_lengths.append(edge_length)

    return edge_lengths

def calculate_average_edge_length(vertices):
    """计算形状所有边的平均长度"""
    edge_lengths = calculate_all_edge_lengths(vertices)
    if not edge_lengths:
        return 0.0
    return sum(edge_lengths) / len(edge_lengths)

def calculate_physical_size_from_pixels(pixel_size, reference_pixels, reference_physical_size):
    """根据参考物理尺寸计算实际物理尺寸"""
    if reference_pixels <= 0:
        return 0.0
    return (pixel_size * reference_physical_size) / reference_pixels

def is_point_inside_polygon(point, polygon_points):
    """判断点是否在多边形内部"""
    try:
        if polygon_points is None or len(polygon_points) < 3:
            return False
        
        # 将polygon_points转换为正确的格式
        if isinstance(polygon_points, np.ndarray):
            if len(polygon_points.shape) == 3:
                # 已经是正确格式 [[[x,y]], [[x,y]], ...]
                polygon_contour = polygon_points.astype(np.int32)
            elif len(polygon_points.shape) == 2:
                # 格式 [[x,y], [x,y], ...] 需要转换
                polygon_contour = polygon_points.reshape(-1, 1, 2).astype(np.int32)
            else:
                print(f"警告：多边形数据格式不正确，shape={polygon_points.shape}")
                return False
        else:
            # 转换为OpenCV期望的格式
            try:
                polygon_contour = np.array(polygon_points, dtype=np.int32).reshape(-1, 1, 2)
            except Exception as e:
                print(f"警告：无法转换多边形数据格式: {e}")
                return False
        
        # 确保点坐标是数值类型
        try:
            px, py = float(point[0]), float(point[1])
        except (ValueError, TypeError, IndexError) as e:
            print(f"警告：点坐标格式错误: {e}, point={point}")
            return False
        
        result = cv2.pointPolygonTest(polygon_contour, (px, py), False)
        return result >= 0  # >= 0 表示在多边形内部或边界上
        
    except Exception as e:
        print(f"判断点是否在多边形内出错: {e}, point={point}")
        return False

def calculate_triangle_area(p1, p2, p3):
    """计算三角形面积"""
    try:
        # 使用向量叉积计算三角形面积
        # Area = 0.5 * |AB × AC|
        ab = np.array([p2[0] - p1[0], p2[1] - p1[1]])
        ac = np.array([p3[0] - p1[0], p3[1] - p1[1]])
        cross_product = ab[0] * ac[1] - ab[1] * ac[0]
        return abs(cross_product) / 2.0
    except Exception as e:
        print(f"计算三角形面积出错: {e}")
        return 0.0

def calculate_edge_overlap_with_polygon(edge_start, edge_end, polygon_points, overlap_threshold=0.5):
    """
    计算边与多边形的重叠程度
    返回重叠比例 (0.0 - 1.0)
    
    参数:
    - edge_start: 边的起点
    - edge_end: 边的终点  
    - polygon_points: 多边形顶点
    - overlap_threshold: 重叠阈值，超过此值认为边在多边形内
    """
    try:
        if edge_start == edge_end:
            print("警告：边的起点和终点相同")
            return 0.0
        
        # 计算边长，如果太短则返回0
        edge_length = calculate_distance(edge_start, edge_end)
        if edge_length < 1.0:  # 边长小于1像素
            print(f"警告：边长太短 ({edge_length:.2f} pixels)")
            return 0.0
        
        # 在边上采样多个点进行检测
        # 根据边长动态调整采样点数量，但设置最小和最大值
        sample_count = max(10, min(50, int(edge_length / 2)))  # 10-50个采样点
        inside_count = 0
        
        for i in range(sample_count + 1):
            t = i / sample_count
            # 线性插值计算采样点
            sample_x = edge_start[0] + t * (edge_end[0] - edge_start[0])
            sample_y = edge_start[1] + t * (edge_end[1] - edge_start[1])
            sample_point = (sample_x, sample_y)
            
            # 检查采样点是否在多边形内
            if is_point_inside_polygon(sample_point, polygon_points):
                inside_count += 1
        
        # 计算重叠比例
        overlap_ratio = inside_count / (sample_count + 1)
        
        print(f"    边重叠分析: 长度={edge_length:.1f}px, 采样={sample_count+1}点, 内部={inside_count}点, 比例={overlap_ratio:.3f}")
        return overlap_ratio
        
    except Exception as e:
        print(f"计算边与多边形重叠度出错: {e}")
        return 0.0

def find_closest_point_on_inner_contours(target_point, inner_contours):
    """
    在内轮廓上找到离目标点最近的点

    参数:
    - target_point: 目标点 (x, y)
    - inner_contours: 内轮廓数据列表

    返回:
    - closest_point: 最近的点 (x, y)，如果没找到返回None
    """
    if not inner_contours:
        return None

    min_distance = float('inf')
    closest_point = None

    try:
        for contour in inner_contours:
            for point in contour:
                # 处理OpenCV轮廓点格式
                if len(point.shape) == 2 and point.shape[1] == 2:
                    # 格式: [[x, y]]
                    px, py = point[0]
                elif len(point.shape) == 1 and len(point) == 2:
                    # 格式: [x, y]
                    px, py = point
                else:
                    continue

                distance = calculate_distance(target_point, (px, py))
                if distance < min_distance:
                    min_distance = distance
                    closest_point = (int(px), int(py))

    except Exception as e:
        print(f"      查找最近点时出错: {e}")

    return closest_point

def calculate_polygon_area(points):
    """
    使用鞋带公式计算多边形面积

    参数:
    - points: 多边形顶点列表 [(x1, y1), (x2, y2), ...]

    返回:
    - area: 多边形面积
    """
    if len(points) < 3:
        return 0.0

    area = 0.0
    n = len(points)

    for i in range(n):
        j = (i + 1) % n
        area += points[i][0] * points[j][1]
        area -= points[j][0] * points[i][1]

    return abs(area) / 2.0

def calculate_point_to_line_distance(point, line_start, line_end):
    """
    计算点到直线的垂直距离

    参数:
    - point: 点坐标 (x, y)
    - line_start: 直线起点 (x, y)
    - line_end: 直线终点 (x, y)

    返回:
    - distance: 垂直距离
    """
    try:
        # 直线方程: ax + by + c = 0
        # 从两点式转换: (y2-y1)x - (x2-x1)y + (x2-x1)y1 - (y2-y1)x1 = 0
        x1, y1 = line_start
        x2, y2 = line_end
        px, py = point

        # 如果是同一个点，返回点到点的距离
        if x1 == x2 and y1 == y2:
            return calculate_distance(point, line_start)

        # 计算直线参数
        a = y2 - y1
        b = x1 - x2
        c = (x2 - x1) * y1 - (y2 - y1) * x1

        # 点到直线距离公式: |ax + by + c| / sqrt(a² + b²)
        distance = abs(a * px + b * py + c) / np.sqrt(a * a + b * b)
        return distance

    except Exception as e:
        print(f"      计算点到直线距离时出错: {e}")
        return 0.0

def check_white_points_on_line(line_start, line_end, vertices, corner_marks, tolerance=4):
    """
    检查线段上是否有白点（标记为0的拐点）

    参数:
    - line_start: 线段起点
    - line_end: 线段终点
    - vertices: 所有顶点
    - corner_marks: 拐点标记
    - tolerance: 容差像素

    返回:
    - has_white_points: 是否有白点
    """
    try:
        for i, mark in enumerate(corner_marks):
            if mark == 0:  # 白点（无效拐点）
                point = vertices[i]
                distance = calculate_point_to_line_distance(point, line_start, line_end)
                if distance <= tolerance:
                    print(f"      找到白点 {i} 在线段附近，距离: {distance:.2f} 像素")
                    return True
        return False
    except Exception as e:
        print(f"      检查白点时出错: {e}")
        return False

def handle_single_isolated_corner(vertices, corner_marks, isolated_index, inner_contours):
    """
    处理单个孤立拐点的情况

    算法步骤:
    1. 找到孤立拐点前后相邻的两个白点
    2. 在内轮廓上找到离孤立拐点最近的内点
    3. 构成四边形并计算面积
    4. 延长两条线段，计算内点到延长线的垂直距离
    5. 比较正方形面积，选择较大的作为有效边
    """
    try:
        isolated_point = vertices[isolated_index]
        print(f"      孤立拐点索引: {isolated_index}, 坐标: {isolated_point}")

        # 找到前后相邻的两个白点
        prev_white_index = None
        next_white_index = None

        # 向前查找白点
        for i in range(1, len(vertices)):
            check_index = (isolated_index - i) % len(vertices)
            if corner_marks[check_index] == 0:
                prev_white_index = check_index
                break

        # 向后查找白点
        for i in range(1, len(vertices)):
            check_index = (isolated_index + i) % len(vertices)
            if corner_marks[check_index] == 0:
                next_white_index = check_index
                break

        if prev_white_index is None or next_white_index is None:
            print(f"      未找到足够的相邻白点")
            return 0.0

        prev_white_point = vertices[prev_white_index]
        next_white_point = vertices[next_white_index]
        print(f"      前白点索引: {prev_white_index}, 坐标: {prev_white_point}")
        print(f"      后白点索引: {next_white_index}, 坐标: {next_white_point}")

        # 在内轮廓上找到离孤立拐点最近的内点
        inner_point = find_closest_point_on_inner_contours(isolated_point, inner_contours)
        if inner_point is None:
            print(f"      未找到内轮廓上的最近点")
            return 0.0

        print(f"      内点坐标: {inner_point}")

        # 构成四边形并计算面积
        quadrilateral_points = [isolated_point, prev_white_point, inner_point, next_white_point]
        quad_area = calculate_polygon_area(quadrilateral_points)
        print(f"      四边形面积: {quad_area:.2f}")

        # 延长两条线段，计算内点到延长线的垂直距离
        dist1 = calculate_point_to_line_distance(inner_point, isolated_point, prev_white_point)
        dist2 = calculate_point_to_line_distance(inner_point, isolated_point, next_white_point)
        print(f"      内点到延长线1的距离: {dist1:.2f}")
        print(f"      内点到延长线2的距离: {dist2:.2f}")

        # 计算两个正方形面积
        square_area1 = dist1 * dist1
        square_area2 = dist2 * dist2
        print(f"      正方形1面积: {square_area1:.2f}")
        print(f"      正方形2面积: {square_area2:.2f}")

        # 选择面积更大的正方形的边长
        if square_area1 > quad_area and square_area1 >= square_area2:
            selected_edge = dist1
            print(f"      选择正方形1的边长: {selected_edge:.2f}")
        elif square_area2 > quad_area:
            selected_edge = dist2
            print(f"      选择正方形2的边长: {selected_edge:.2f}")
        else:
            print(f"      所有正方形面积都不大于四边形面积，不添加特殊边")
            return 0.0

        return selected_edge

    except Exception as e:
        print(f"      处理单个孤立拐点时出错: {e}")
        return 0.0

def handle_two_isolated_corners(vertices, corner_marks, isolated_corners):
    """
    处理两个孤立拐点的情况

    算法步骤:
    1. 连接两个孤立拐点
    2. 检查线段上是否有白点
    3. 如果有白点：直接用线段长度
    4. 如果没有白点：用线段长度/√2
    """
    try:
        point1 = vertices[isolated_corners[0]]
        point2 = vertices[isolated_corners[1]]
        print(f"      孤立拐点1索引: {isolated_corners[0]}, 坐标: {point1}")
        print(f"      孤立拐点2索引: {isolated_corners[1]}, 坐标: {point2}")

        # 计算两点间距离
        line_length = calculate_distance(point1, point2)
        print(f"      连接线段长度: {line_length:.2f}")

        # 检查线段上是否有白点
        has_white_points = check_white_points_on_line(point1, point2, vertices, corner_marks)

        if has_white_points:
            print(f"      线段上有白点，使用原始长度: {line_length:.2f}")
            return line_length
        else:
            # 除以√2（把线段当做正方形对角线）
            edge_length = line_length / np.sqrt(2)
            print(f"      线段上无白点，使用长度/√2: {edge_length:.2f}")
            return edge_length

    except Exception as e:
        print(f"      处理两个孤立拐点时出错: {e}")
        return 0.0

def handle_three_isolated_corners(vertices, isolated_corners):
    """
    处理三个孤立拐点的情况

    算法步骤:
    1. 找到序列中间的孤立拐点
    2. 连接另外两个孤立拐点
    3. 使用连接线段的长度
    """
    try:
        # 按索引排序，找到中间的拐点
        sorted_corners = sorted(isolated_corners)
        middle_index = sorted_corners[1]
        first_index = sorted_corners[0]
        last_index = sorted_corners[2]

        print(f"      三个孤立拐点索引: {sorted_corners}")
        print(f"      忽略中间拐点: {middle_index}")
        print(f"      连接拐点: {first_index} - {last_index}")

        point1 = vertices[first_index]
        point2 = vertices[last_index]
        print(f"      拐点1坐标: {point1}")
        print(f"      拐点2坐标: {point2}")

        # 计算连接线段长度
        line_length = calculate_distance(point1, point2)
        print(f"      连接线段长度: {line_length:.2f}")

        return line_length

    except Exception as e:
        print(f"      处理三个孤立拐点时出错: {e}")
        return 0.0

def process_isolated_corners_special_cases(vertices, corner_marks, isolated_corners, inner_contours):
    """
    处理孤立拐点的特殊情况

    参数:
    - vertices: 多边形顶点列表
    - corner_marks: 拐点标记列表
    - isolated_corners: 孤立拐点索引列表
    - inner_contours: 内轮廓数据列表

    返回:
    - special_edges: 特殊处理得到的边长列表
    """
    special_edges = []
    isolated_count = len(isolated_corners)

    try:
        if isolated_count == 1:
            # 情况1：孤立拐点数量为1
            print("    处理情况1：孤立拐点数量为1")
            edge_length = handle_single_isolated_corner(vertices, corner_marks, isolated_corners[0], inner_contours)
            if edge_length > 0:
                special_edges.append(edge_length)

        elif isolated_count == 2:
            # 情况2：孤立拐点数量为2
            print("    处理情况2：孤立拐点数量为2")
            edge_length = handle_two_isolated_corners(vertices, corner_marks, isolated_corners)
            if edge_length > 0:
                special_edges.append(edge_length)

        elif isolated_count == 3:
            # 情况3：孤立拐点数量为3
            print("    处理情况3：孤立拐点数量为3")
            edge_length = handle_three_isolated_corners(vertices, isolated_corners)
            if edge_length > 0:
                special_edges.append(edge_length)

        else:
            print(f"    不支持的孤立拐点数量: {isolated_count}")

    except Exception as e:
        print(f"    孤立拐点特殊处理出错: {e}")

    return special_edges

def process_polygon_corners_and_find_min_edge(polygon_approx, reference_pixels, reference_physical_size,
                                            overlap_threshold=0.5, enable_isolated_detection=True, inner_contours=None):
    """
    处理多边形拐点，计算最小边长并转换为物理长度

    参数:
    - polygon_approx: 多边形轮廓点
    - reference_pixels: 参考像素长度
    - reference_physical_size: 参考物理长度
    - overlap_threshold: 重叠阈值
    - enable_isolated_detection: 是否启用孤立拐点检测，默认True
    - inner_contours: 内轮廓数据列表，用于孤立拐点特殊处理

    拐点分类:
    - 标记为0: 无效拐点（重叠度 < 阈值）
    - 标记为1: 普通有效拐点（重叠度 >= 阈值，且不是孤立的）- 参与边长计算
    - 标记为2: 孤立拐点（重叠度 >= 阈值，但前后相邻拐点都无效）- 不参与边长计算

    返回:
    - min_edge_length_pixels: 最小边长（像素）
    - min_edge_length_physical: 最小边长（物理长度）
    - corner_marks: 拐点标记列表（0/1/2）
    - min_edge_indices: 最小边的拐点索引
    """
    try:
        if polygon_approx is None or len(polygon_approx) < 3:
            print(f"多边形数据无效: {polygon_approx}")
            return 0.0, 0.0, [], []
        
        if reference_pixels <= 0 or reference_physical_size <= 0:
            print(f"参考尺寸无效: pixels={reference_pixels}, physical={reference_physical_size}")
            return 0.0, 0.0, [], []
        # 提取顶点坐标
        vertices = []
        for pt in polygon_approx:
            try:
                if isinstance(pt, np.ndarray):
                    if len(pt.shape) == 2 and pt.shape[0] == 1 and pt.shape[1] >= 2:
                        # OpenCV标准格式: [[x, y]]
                        vertices.append((int(pt[0][0]), int(pt[0][1])))
                    elif len(pt.shape) == 1 and len(pt) >= 2:
                        # 一维数组格式: [x, y]
                        vertices.append((int(pt[0]), int(pt[1])))
                    else:
                        print(f"未知的顶点格式: shape={pt.shape}, 数据={pt}")
                        continue
                elif isinstance(pt, (list, tuple)) and len(pt) >= 2:
                    # 列表或元组格式
                    if isinstance(pt[0], (list, tuple)):
                        if len(pt[0]) >= 2:
                            # 嵌套格式: [[x, y]]
                            vertices.append((int(pt[0][0]), int(pt[0][1])))
                        else:
                            print(f"嵌套顶点数据长度不足: {pt}")
                            continue
                    else:
                        # 直接格式: [x, y]
                        if len(pt) >= 2:
                            vertices.append((int(pt[0]), int(pt[1])))
                        else:
                            print(f"顶点数据长度不足: {pt}")
                            continue
                else:
                    print(f"未知的顶点数据类型: {type(pt)}, 数据={pt}")
                    continue
            except (IndexError, ValueError, TypeError) as e:
                print(f"顶点坐标提取错误: {e}, 数据={pt}")
                continue
        
        if len(vertices) < 3:
            print(f"顶点数量不足: {len(vertices)}")
            return 0.0, 0.0, [], []
            
        print(f"处理多边形拐点: {len(vertices)} 个顶点，重叠阈值: {overlap_threshold}")

        # 第一步：基于重叠度进行初始标记
        initial_marks = []

        for i in range(len(vertices)):
            current_vertex = vertices[i]
            prev_vertex = vertices[(i - 1) % len(vertices)]
            next_vertex = vertices[(i + 1) % len(vertices)]

            print(f"  处理拐点 {i}: 当前{current_vertex}, 前{prev_vertex}, 后{next_vertex}")

            # 计算当前拐点在相邻两点形成的三角形中对应的边
            # 这条边是连接前一个和下一个顶点的边
            edge_start = prev_vertex
            edge_end = next_vertex

            # 计算该边与多边形的重叠程度
            overlap_ratio = calculate_edge_overlap_with_polygon(
                edge_start, edge_end, polygon_approx, overlap_threshold
            )

            # 根据重叠程度进行初始标记
            if overlap_ratio >= overlap_threshold:
                initial_marks.append(1)
                print(f"    拐点 {i} 初始标记为 1 (重叠度: {overlap_ratio:.3f} >= {overlap_threshold})")
            else:
                initial_marks.append(0)
                print(f"    拐点 {i} 初始标记为 0 (重叠度: {overlap_ratio:.3f} < {overlap_threshold})")

        # 第二步：识别孤立拐点并进行最终标记（如果启用）
        corner_marks = []
        isolated_count = 0

        if enable_isolated_detection:
            print(f"  识别孤立拐点:")
            for i in range(len(vertices)):
                current_mark = initial_marks[i]
                prev_mark = initial_marks[(i - 1) % len(vertices)]
                next_mark = initial_marks[(i + 1) % len(vertices)]

                # 检查是否为孤立拐点：当前为有效拐点(1)，但前后都是无效拐点(0)
                if current_mark == 1 and prev_mark == 0 and next_mark == 0:
                    corner_marks.append(2)  # 标记为孤立拐点
                    isolated_count += 1
                    print(f"    拐点 {i} 标记为 2 (孤立拐点: 前{prev_mark}-当前{current_mark}-后{next_mark})")
                else:
                    corner_marks.append(current_mark)  # 保持原始标记
                    if current_mark == 1:
                        print(f"    拐点 {i} 保持标记为 1 (非孤立: 前{prev_mark}-当前{current_mark}-后{next_mark})")
                    else:
                        print(f"    拐点 {i} 保持标记为 0 (无效拐点)")

            print(f"  识别结果: 发现 {isolated_count} 个孤立拐点")
        else:
            # 如果未启用孤立拐点检测，直接使用初始标记
            corner_marks = initial_marks[:]
            print(f"  孤立拐点检测已禁用，使用初始标记")
        
        # 查找相邻的有效拐点，计算它们之间的边长
        # 只有标记为1的普通有效拐点才参与边长计算，孤立拐点（标记为2）不参与
        valid_edges = []
        valid_edge_indices = []

        print(f"  查找相邻的有效拐点（仅标记为1）:")
        for i in range(len(vertices)):
            current_mark = corner_marks[i]
            next_mark = corner_marks[(i + 1) % len(vertices)]

            print(f"    检查边 {i}-{(i + 1) % len(vertices)}: 标记 {current_mark}-{next_mark}")

            # 只有当前拐点和下一个拐点都标记为1时才认为是有效边
            if current_mark == 1 and next_mark == 1:
                current_vertex = vertices[i]
                next_vertex = vertices[(i + 1) % len(vertices)]

                # 计算边长
                edge_length = calculate_distance(current_vertex, next_vertex)

                # 检查边长是否有效
                if edge_length > 0:
                    valid_edges.append(edge_length)
                    valid_edge_indices.append((i, (i + 1) % len(vertices)))
                    print(f"      有效边 {i}-{(i + 1) % len(vertices)}: {edge_length:.2f} 像素")
                else:
                    print(f"      警告：边长为0，跳过边 {i}-{(i + 1) % len(vertices)}")
            else:
                if current_mark == 2 or next_mark == 2:
                    print(f"      跳过边 {i}-{(i + 1) % len(vertices)} (涉及孤立拐点)")
                else:
                    print(f"      跳过边 {i}-{(i + 1) % len(vertices)} (涉及无效拐点)")

        if not valid_edges:
            print("  没有找到有效边（相邻的标记为1的拐点）")
            return 0.0, 0.0, corner_marks, []

        # 孤立拐点特殊处理
        isolated_corners = [i for i, mark in enumerate(corner_marks) if mark == 2]
        isolated_count = len(isolated_corners)

        print(f"  孤立拐点数量: {isolated_count}")
        if isolated_count > 0:
            print(f"  孤立拐点索引: {isolated_corners}")

            # 根据孤立拐点数量进行不同处理
            special_edges = process_isolated_corners_special_cases(
                vertices, corner_marks, isolated_corners, inner_contours
            )

            if special_edges:
                print(f"  特殊处理得到 {len(special_edges)} 条边:")
                for i, edge_length in enumerate(special_edges):
                    print(f"    特殊边 {i+1}: {edge_length:.2f} 像素")
                valid_edges.extend(special_edges)

        # 找出最小边长
        try:
            min_edge_length_pixels = min(valid_edges)
            min_edge_index = valid_edges.index(min_edge_length_pixels)
            min_edge_indices = valid_edge_indices[min_edge_index]
            
            print(f"  有效边列表: {[f'{edge:.2f}' for edge in valid_edges]}")
            print(f"  最小边长: {min_edge_length_pixels:.2f} 像素 (索引 {min_edge_index})")
            print(f"  最小边连接拐点: {min_edge_indices[0]} - {min_edge_indices[1]}")
            
        except (ValueError, IndexError) as e:
            print(f"查找最小边长时出错: {e}")
            return 0.0, 0.0, corner_marks, []
        
        # 转换为物理长度
        try:
            min_edge_length_physical = calculate_physical_size_from_pixels(
                min_edge_length_pixels, reference_pixels, reference_physical_size
            )
            
            print(f"  物理长度转换: {min_edge_length_pixels:.2f}px × {reference_physical_size:.2f}cm / {reference_pixels:.2f}px = {min_edge_length_physical:.2f}cm")
            
        except Exception as e:
            print(f"物理长度转换出错: {e}")
            return min_edge_length_pixels, 0.0, corner_marks, min_edge_indices
        
        return min_edge_length_pixels, min_edge_length_physical, corner_marks, min_edge_indices
        
    except Exception as e:
        print(f"处理多边形拐点时出错: {e}")
        return 0.0, 0.0, [], []

def validate_polygon_analysis_result(vertices, corner_marks, min_edge_indices, min_edge_length_pixels):
    """
    验证多边形分析结果的合理性
    """
    try:
        if not vertices or not corner_marks:
            return False, "顶点或标记为空"
        
        if len(vertices) != len(corner_marks):
            return False, f"顶点数量({len(vertices)})与标记数量({len(corner_marks)})不匹配"
        
        # 统计不同类型的拐点数量
        valid_count = sum(1 for mark in corner_marks if mark == 1)  # 普通有效拐点
        isolated_count = sum(1 for mark in corner_marks if mark == 2)  # 孤立拐点
        invalid_count = sum(1 for mark in corner_marks if mark == 0)  # 无效拐点

        if valid_count == 0:
            return False, "没有拐点被标记为1（普通有效拐点）"

        if invalid_count == 0 and isolated_count == 0:
            return True, f"所有{len(corner_marks)}个拐点都被标记为1（普通有效拐点）"

        if min_edge_indices and len(min_edge_indices) == 2:
            start_idx, end_idx = min_edge_indices
            if not (0 <= start_idx < len(vertices) and 0 <= end_idx < len(vertices)):
                return False, f"最小边索引超出范围: {min_edge_indices}"

            start_mark = corner_marks[start_idx]
            end_mark = corner_marks[end_idx]
            # 最小边的端点必须都标记为1
            if not (start_mark == 1 and end_mark == 1):
                return False, f"最小边的端点未被标记为1: {start_idx}({start_mark}), {end_idx}({end_mark})"

        if min_edge_length_pixels <= 0:
            return False, f"最小边长无效: {min_edge_length_pixels}"

        return True, f"验证通过: {valid_count}/{len(corner_marks)}个普通有效拐点（孤立{isolated_count}个，无效{invalid_count}个）"
        
    except Exception as e:
        return False, f"验证过程出错: {e}"

def draw_polygon_corner_analysis(img_result, vertices, corner_marks, min_edge_indices,
                               min_edge_length_physical):
    """
    在图像上绘制多边形拐点分析结果
    
    参数:
    - img_result: 结果图像
    - vertices: 顶点列表
    - corner_marks: 拐点标记列表
    - min_edge_indices: 最小边的拐点索引
    - min_edge_length_physical: 最小边物理长度
    """
    try:
        if not vertices or not corner_marks or len(vertices) != len(corner_marks):
            print(f"绘制参数错误: vertices={len(vertices) if vertices else 0}, marks={len(corner_marks) if corner_marks else 0}")
            return
        
        # 绘制所有顶点和标记
        for i, vertex in enumerate(vertices):
            try:
                x, y = int(vertex[0]), int(vertex[1])

                # 根据标记选择颜色和显示
                if i < len(corner_marks):
                    mark = corner_marks[i]
                    if mark == 1:
                        color = image.COLOR_GREEN  # 标记为1的普通有效拐点用绿色
                        img_result.draw_circle(x, y, 5, color, thickness=-1)
                        img_result.draw_string(x + 8, y - 8, "1", color)
                    elif mark == 2:
                        color = image.COLOR_BLUE   # 标记为2的孤立拐点用蓝色
                        img_result.draw_circle(x, y, 6, color, thickness=-1)  # 稍大一点以区分
                        img_result.draw_string(x + 8, y - 8, "2", color)
                    else:  # mark == 0
                        color = image.COLOR_RED    # 标记为0的无效拐点用红色
                        img_result.draw_circle(x, y, 5, color, thickness=-1)
                        img_result.draw_string(x + 8, y - 8, "0", color)
                else:
                    # 默认情况
                    color = image.COLOR_RED
                    img_result.draw_circle(x, y, 5, color, thickness=-1)
                    img_result.draw_string(x + 8, y - 8, "?", color)
            except (ValueError, TypeError, IndexError) as e:
                print(f"绘制顶点 {i} 时出错: {e}, vertex={vertex}")
                continue
        
        # 高亮显示最小边
        if min_edge_indices and len(min_edge_indices) == 2:
            start_idx, end_idx = min_edge_indices
            if (0 <= start_idx < len(vertices) and 0 <= end_idx < len(vertices)):
                try:
                    start_point = vertices[start_idx]
                    end_point = vertices[end_idx]
                    
                    start_x, start_y = int(start_point[0]), int(start_point[1])
                    end_x, end_y = int(end_point[0]), int(end_point[1])
                    
                    # 用黄色粗线高亮最小边
                    img_result.draw_line(start_x, start_y, end_x, end_y, image.COLOR_YELLOW, 4)
                    
                    # 在边的中点显示物理长度
                    mid_x = (start_x + end_x) // 2
                    mid_y = (start_y + end_y) // 2
                    length_text = f"{min_edge_length_physical:.2f}cm"
                    img_result.draw_string(mid_x - 20, mid_y - 10, length_text, image.COLOR_YELLOW)
                    
                    print(f"高亮显示最小边: 从顶点{start_idx}({start_x},{start_y}) 到顶点{end_idx}({end_x},{end_y})")
                except (ValueError, TypeError, IndexError) as e:
                    print(f"绘制最小边时出错: {e}, indices={min_edge_indices}")
            else:
                print(f"最小边索引超出范围: {min_edge_indices}, vertices count={len(vertices)}")
        
        # 在图像左上角显示最小边长信息
        if min_edge_length_physical > 0:
            info_text = f"Min Edge: {min_edge_length_physical:.2f}cm"
            img_result.draw_string(10, 25, info_text, image.COLOR_YELLOW)
        
    except Exception as e:
        print(f"绘制多边形拐点分析结果时出错: {e}")

def get_second_largest_rect_longest_edge(max_rectangles):
    """获取第二大矩形的最长边像素长度"""
    if len(max_rectangles) < 2:
        return 0.0

    # 获取第二大矩形的顶点
    _, _, approx = max_rectangles[1]  # 第二大矩形
    vertices = [tuple(pt[0]) for pt in approx]

    # 计算最长边
    return calculate_longest_edge(vertices)

def update_inner_shape_stats(shape_type, vertices=None, radius=None, inner_shapes_stats=None,
                            max_rectangles=None, second_largest_rect_physical_length=50.0):
    """更新框内图形统计数据，包括物理尺寸计算"""
    if inner_shapes_stats is None:
        return

    # 获取第二大矩形的最长边像素长度作为参考
    reference_pixels = get_second_largest_rect_longest_edge(max_rectangles) if max_rectangles else 0.0

    if shape_type == "Triangle" and vertices:
        # 计算三角形的平均边长（像素）
        avg_edge_pixels = calculate_average_edge_length(vertices)
        # 计算物理尺寸
        avg_edge_physical = calculate_physical_size_from_pixels(
            avg_edge_pixels, reference_pixels, second_largest_rect_physical_length
        ) if reference_pixels > 0 else 0.0

        inner_shapes_stats['triangles']['count'] += 1
        inner_shapes_stats['triangles']['total_edge_length'] += avg_edge_pixels
        inner_shapes_stats['triangles']['total_edge_length_physical'] += avg_edge_physical
        inner_shapes_stats['triangles']['avg_edge_length'] = (
            inner_shapes_stats['triangles']['total_edge_length'] /
            inner_shapes_stats['triangles']['count']
        )
        inner_shapes_stats['triangles']['avg_edge_length_physical'] = (
            inner_shapes_stats['triangles']['total_edge_length_physical'] /
            inner_shapes_stats['triangles']['count']
        )

    elif shape_type == "Quad" and vertices:
        # 计算四边形的平均边长（像素）
        avg_edge_pixels = calculate_average_edge_length(vertices)
        # 计算物理尺寸
        avg_edge_physical = calculate_physical_size_from_pixels(
            avg_edge_pixels, reference_pixels, second_largest_rect_physical_length
        ) if reference_pixels > 0 else 0.0

        inner_shapes_stats['quadrilaterals']['count'] += 1
        inner_shapes_stats['quadrilaterals']['total_edge_length'] += avg_edge_pixels
        inner_shapes_stats['quadrilaterals']['total_edge_length_physical'] += avg_edge_physical
        inner_shapes_stats['quadrilaterals']['avg_edge_length'] = (
            inner_shapes_stats['quadrilaterals']['total_edge_length'] /
            inner_shapes_stats['quadrilaterals']['count']
        )
        inner_shapes_stats['quadrilaterals']['avg_edge_length_physical'] = (
            inner_shapes_stats['quadrilaterals']['total_edge_length_physical'] /
            inner_shapes_stats['quadrilaterals']['count']
        )

    elif shape_type == "Circle" and radius:
        # 计算圆形的物理半径
        radius_physical = calculate_physical_size_from_pixels(
            radius, reference_pixels, second_largest_rect_physical_length
        ) if reference_pixels > 0 else 0.0

        inner_shapes_stats['circles']['count'] += 1
        inner_shapes_stats['circles']['total_radius'] += radius
        inner_shapes_stats['circles']['total_radius_physical'] += radius_physical
        inner_shapes_stats['circles']['avg_radius'] = (
            inner_shapes_stats['circles']['total_radius'] /
            inner_shapes_stats['circles']['count']
        )
        inner_shapes_stats['circles']['avg_radius_physical'] = (
            inner_shapes_stats['circles']['total_radius_physical'] /
            inner_shapes_stats['circles']['count']
        )

    elif shape_type == "Polygon" and vertices:
        # 计算多边形的平均边长（像素）
        avg_edge_pixels = calculate_average_edge_length(vertices)
        # 计算物理尺寸
        avg_edge_physical = calculate_physical_size_from_pixels(
            avg_edge_pixels, reference_pixels, second_largest_rect_physical_length
        ) if reference_pixels > 0 else 0.0

        inner_shapes_stats['polygons']['count'] += 1
        inner_shapes_stats['polygons']['total_edge_length'] += avg_edge_pixels
        inner_shapes_stats['polygons']['total_edge_length_physical'] += avg_edge_physical
        inner_shapes_stats['polygons']['avg_edge_length'] = (
            inner_shapes_stats['polygons']['total_edge_length'] /
            inner_shapes_stats['polygons']['count']
        )
        inner_shapes_stats['polygons']['avg_edge_length_physical'] = (
            inner_shapes_stats['polygons']['total_edge_length_physical'] /
            inner_shapes_stats['polygons']['count']
        )

def reset_inner_shape_stats(inner_shapes_stats):
    """重置框内图形统计数据"""
    inner_shapes_stats['triangles'] = {
        'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
        'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0
    }
    inner_shapes_stats['quadrilaterals'] = {
        'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
        'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0
    }
    inner_shapes_stats['circles'] = {
        'count': 0, 'avg_radius': 0.0, 'total_radius': 0.0,
        'avg_radius_physical': 0.0, 'total_radius_physical': 0.0
    }
    inner_shapes_stats['polygons'] = {
        'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
        'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0
    }

def collect_fine_polygons_from_quads(quadrilaterals, img_cv, edges, min_area,
                                    enable_preprocess, preprocess_started, max_rectangles,
                                    preprocess_stable_frames, preprocess_stable_threshold):
    """
    从所有四边形区域收集精细检测的多边形，用于四边形过滤
    返回: [(cx, cy, approx), ...] 格式的多边形列表
    """
    global use_roi_optimization, roi_valid, cached_roi_rect

    collected_polygons = []

    # 只在预处理完成后才进行多边形检测
    if not (enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
            preprocess_stable_frames >= preprocess_stable_threshold):
        return collected_polygons

    # 临时减小epsilon_factor以提高精度
    fine_epsilon_factor = 0.01

    try:
        for quad_info in quadrilaterals:
            quad_approx = quad_info['approx']

            # 在ROI模式下，四边形坐标已经是ROI坐标系的，无需额外检查
            # 因为四边形是在edges图像上检测的，而edges图像就是ROI区域

            # 创建四边形区域的掩码
            if edges.shape != img_cv.shape[:2]:
                # ROI模式：调整坐标
                roi_x, roi_y, roi_w, roi_h = cached_roi_rect
                adjusted_quad = quad_approx.copy()
                for i in range(len(adjusted_quad)):
                    adjusted_quad[i][0][0] -= roi_x
                    adjusted_quad[i][0][1] -= roi_y

                mask = np.zeros(edges.shape, dtype=np.uint8)
                cv2.fillPoly(mask, [adjusted_quad.reshape(-1, 2)], 255)
            else:
                # 非ROI模式
                mask = np.zeros(edges.shape, dtype=np.uint8)
                cv2.fillPoly(mask, [quad_approx.reshape(-1, 2)], 255)

            # 在四边形区域内查找轮廓
            masked_edges = cv2.bitwise_and(edges, mask)
            contours, _ = cv2.findContours(masked_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                area = cv2.contourArea(contour)
                if area < min_area:
                    continue

                # 使用精细的epsilon_factor
                epsilon = fine_epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                num_vertices = len(approx)

                # 只收集多边形（5个或更多顶点）
                if num_vertices >= 5:
                    # 计算中心点
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])

                        # 如果是ROI模式，需要转换回原图坐标
                        if edges.shape != img_cv.shape[:2] and roi_valid and cached_roi_rect is not None:
                            roi_x, roi_y, roi_w, roi_h = cached_roi_rect
                            cx += roi_x
                            cy += roi_y

                            # 也需要调整approx坐标
                            adjusted_approx = approx.copy()
                            for i in range(len(adjusted_approx)):
                                adjusted_approx[i][0][0] += roi_x
                                adjusted_approx[i][0][1] += roi_y
                            approx = adjusted_approx

                        # 确保坐标格式正确：转换为整数并保持正确的数组格式
                        formatted_approx = np.array([[int(pt[0][0]), int(pt[0][1])] for pt in approx], dtype=np.int32)
                        formatted_approx = formatted_approx.reshape(-1, 1, 2)  # OpenCV期望的格式

                        collected_polygons.append((cx, cy, formatted_approx))
                        # 简化调试输出
                        pass

    except Exception as e:
        print(f"收集精细多边形时出错: {e}")

    return collected_polygons

def detect_polygons_in_quad_region(quad_approx, img_cv, edges, min_area,
                                  detected_shapes, duplicate_distance_threshold, duplicate_area_ratio,
                                  img_result, frame_count, shape_tracking_data, vertex_history,
                                  vertex_history_size, position_tolerance, last_frame_shapes,
                                  enable_preprocess, preprocess_started, max_rectangles,
                                  preprocess_stable_frames, preprocess_stable_threshold,
                                  enable_inner_shape_stats=False, inner_shapes_stats=None,
                                  second_largest_rect_physical_length=50.0):
    """
    在四边形区域内检测多边形，使用更精细的epsilon_factor
    """
    global polygon_count, use_roi_optimization, roi_valid, cached_roi_rect

    # 只在预处理完成后才进行多边形检测
    if not (enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
            preprocess_stable_frames >= preprocess_stable_threshold):
        return

    # 在ROI模式下，检查四边形是否完全在ROI区域内
    if (use_roi_optimization and roi_valid and cached_roi_rect is not None and
        edges.shape != img_cv.shape[:2]):
        roi_x, roi_y, roi_w, roi_h = cached_roi_rect

        # 检查四边形的所有顶点是否都在ROI区域内
        all_vertices_in_roi = True
        for point in quad_approx:
            px, py = point[0]
            if not (roi_x <= px < roi_x + roi_w and roi_y <= py < roi_y + roi_h):
                all_vertices_in_roi = False
                break

        if not all_vertices_in_roi:
            print(f"跳过四边形内多边形检测：四边形超出ROI区域")
            return

    # 创建四边形区域的掩码
    # 需要根据edges的实际尺寸来创建掩码
    if edges.shape != img_cv.shape[:2]:
        # ROI模式：edges是ROI区域的尺寸，需要调整quad_approx坐标
        # 获取ROI偏移量
        if roi_valid and cached_roi_rect is not None:
            roi_x, roi_y, roi_w, roi_h = cached_roi_rect
            # 将quad_approx坐标转换为ROI坐标系
            adjusted_quad_approx = quad_approx.copy()
            adjusted_quad_approx[:, 0, 0] -= roi_x  # 调整x坐标
            adjusted_quad_approx[:, 0, 1] -= roi_y  # 调整y坐标

            # 创建ROI尺寸的掩码
            mask = np.zeros(edges.shape, dtype=np.uint8)
            cv2.fillPoly(mask, [adjusted_quad_approx], 255)
        else:
            print("警告：ROI模式下无法获取ROI信息，跳过多边形检测")
            return
    else:
        # 全图模式：直接使用原坐标
        mask = np.zeros(img_cv.shape[:2], dtype=np.uint8)
        cv2.fillPoly(mask, [quad_approx], 255)

    # 在四边形区域内查找轮廓
    masked_edges = cv2.bitwise_and(edges, mask)
    contours, hierarchy = cv2.findContours(masked_edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)

    # 收集内轮廓数据（用于孤立拐点特殊处理）
    inner_contours = []
    if hierarchy is not None:
        for i, contour in enumerate(contours):
            # hierarchy[0][i] = [next, previous, first_child, parent]
            # 如果有父轮廓，说明这是内轮廓
            if hierarchy[0][i][3] != -1:  # 有父轮廓
                inner_contours.append(contour)

    # 临时减小epsilon_factor以提高精度
    fine_epsilon_factor = 0.01

    try:
        for contour in contours:
            # 计算轮廓面积
            area = cv2.contourArea(contour)

            # 过滤掉太小的轮廓
            if area < min_area:
                continue

            # 使用更精细的epsilon_factor进行轮廓近似
            epsilon = fine_epsilon_factor * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            # 根据拐点数量识别形状
            num_vertices = len(approx)

            # 只检测多边形（5个或更多顶点）
            if num_vertices >= 5:
                # 计算轮廓的中心点
                M = cv2.moments(contour)
                if M["m00"] == 0:
                    continue

                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])

                # 如果是ROI模式，需要将坐标转换回原图坐标系
                if edges.shape != img_cv.shape[:2] and roi_valid and cached_roi_rect is not None:
                    roi_x, roi_y, roi_w, roi_h = cached_roi_rect
                    cx += roi_x
                    cy += roi_y
                    # 同时需要调整approx中的顶点坐标
                    adjusted_approx = approx.copy()
                    adjusted_approx[:, 0, 0] += roi_x
                    adjusted_approx[:, 0, 1] += roi_y
                    approx = adjusted_approx

                # 检查是否是重复的多边形
                is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, num_vertices,
                                                duplicate_distance_threshold, duplicate_area_ratio)

                # 如果是重复的，跳过
                if is_duplicate:
                    continue

                shape = f"Polygon({num_vertices})"  # 多边形，显示顶点数
                color = image.COLOR_PURPLE  # 使用紫色表示多边形
                polygon_count += 1

                # 添加到已检测形状列表
                detected_shapes.append((cx, cy, area, num_vertices))

                # 在控制台打印形状信息
                # 简化输出，不打印详细信息
                pass

                # 在图像上标记识别结果
                img_result.draw_string(cx-20, cy, shape, color)
                # 如果启用，显示轮廓面积
                if show_shape_areas:
                    area_text = f"A:{int(area)}"
                    img_result.draw_string(cx-20, cy+15, area_text, color)

                # 提取顶点坐标列表
                # 注意：如果进行了ROI坐标转换，approx已经是原图坐标系的了
                vertices = [tuple(pt[0]) for pt in approx]

                # 查找匹配的形状位置并更新跟踪数据
                shape_position = find_matching_shape_position(cx, cy, "Polygon", shape_tracking_data, position_tolerance)
                shape_position, cx, cy, vertices = update_shape_tracking(
                    shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
                    vertex_history, vertex_history_size, "Polygon"
                )

                # 记录该多边形到当前帧多边形列表
                if "Polygon" not in last_frame_shapes:
                    last_frame_shapes["Polygon"] = []
                last_frame_shapes["Polygon"].append((cx, cy, area))

                # 更新框内图形统计数据（只在预处理完成后统计）
                if enable_inner_shape_stats and enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
                    update_inner_shape_stats("Polygon", vertices=vertices, inner_shapes_stats=inner_shapes_stats,
                                            max_rectangles=max_rectangles, second_largest_rect_physical_length=second_largest_rect_physical_length)

                # 🔥 新增：处理多边形拐点分析
                analysis_completed = False
                if (enable_polygon_corner_analysis and enable_preprocess and preprocess_started and len(max_rectangles) == 2 and 
                    preprocess_stable_frames >= preprocess_stable_threshold):
                    
                    # 获取参考物理长度
                    reference_pixels = get_second_largest_rect_longest_edge(max_rectangles)
                    
                    if reference_pixels > 0:
                        # 处理多边形拐点并计算最小边长
                        min_edge_pixels, min_edge_physical, corner_marks, min_edge_indices = process_polygon_corners_and_find_min_edge(
                            approx, reference_pixels, second_largest_rect_physical_length,
                            polygon_edge_overlap_threshold, enable_isolated_corner_detection, inner_contours
                        )
                        
                        # 验证结果的合理性
                        is_valid, validation_msg = validate_polygon_analysis_result(
                            vertices, corner_marks, min_edge_indices, min_edge_pixels
                        )
                        print(f"        结果验证: {validation_msg}")
                        
                        # 在图像上绘制拐点分析结果
                        if is_valid and min_edge_physical > 0:
                            draw_polygon_corner_analysis(img_result, vertices, corner_marks, min_edge_indices,
                                                       min_edge_physical)
                            analysis_completed = True
                            # 简化多边形分析输出
                            pass

                # 画出轮廓和拐点，并显示边长（仅在拐点分析未完成时）
                if not analysis_completed:
                    draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                                          edge_history, edge_history_size, use_instant_values)

    except Exception as e:
        print(f"四边形区域多边形检测出错: {e}")

def draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                         edge_history, edge_history_size, use_instant_values):
    """绘制形状边缘并计算显示边长"""
    for i in range(len(vertices)):
        pt1 = vertices[i]
        pt2 = vertices[(i+1) % len(vertices)]  # 连接到下一个点，形成闭环

        # 绘制线段（边）
        img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], color, 2)

        # 绘制拐点
        img_result.draw_circle(pt1[0], pt1[1], 3, color, thickness=-1)  # 实心圆

        # 计算边长（两点之间的欧几里得距离）
        edge_length = int(calculate_distance(pt1, pt2))

        # 使用形状位置作为键，确保同一位置的形状共享历史数据
        edge_key = (shape_position, i)
        if edge_key not in edge_history:
            edge_history[edge_key] = []

        # 添加当前边长到历史记录
        edge_history[edge_key].append(edge_length)

        # 保持历史记录在指定大小内
        if len(edge_history[edge_key]) > edge_history_size:
            edge_history[edge_key].pop(0)

        # 获取要显示的边长值（当前值或历史平均值）
        display_length = get_value_from_history(edge_history[edge_key], use_instant_values)

        # 计算边的中点坐标，用于显示边长
        mid_x = (pt1[0] + pt2[0]) // 2
        mid_y = (pt1[1] + pt2[1]) // 2

        # 在边的中点显示边长（根据参数控制）
        if show_edge_lengths:
            length_text = f"{display_length}"
            img_result.draw_string(mid_x, mid_y, length_text, image.COLOR_RED)

        # 在控制台打印边长信息（简化输出）
        if (enable_preprocess and len(max_rectangles) == 2 and 
            frame_count >= preprocess_start_frame + preprocess_stable_threshold):
            # 简化输出，不打印详细的边长信息
            pass

# 设置参数 - 这些参数可能需要根据实际环境调整
binary_threshold = 100    # 二值化阈值
canny_low = 30           # Canny边缘检测低阈值
canny_high = 90         # Canny边缘检测高阈值
min_area = 100           # 最小轮廓面积，识别更小的轮廓
epsilon_factor = 0.1    # 轮廓近似精度因子

# ==================== 距离测量标定参数（重要！请根据实际情况修改）====================
calibration_distance = 130.0    # 标定时的实际距离（厘米）- 请修改为您的实际标定距离
calibration_pixels = 176.0       # 标定时第二大四边形最长边的像素值 - 请修改为您的实际测量值

# ==================== 物理尺寸标定参数（重要！请根据实际情况修改）====================
second_largest_rect_physical_length = 19.1    # 第二大框长边的实际物理长度（厘米）- 请修改为您的实际测量值

# 闭运算参数
enable_closing = False    # 是否启用闭运算
kernel_size = 3          # 闭运算的核大小，越大效果越明显

# 重复检测参数
duplicate_distance_threshold = 15  # 中心点距离阈值，小于此值认为可能是重复
duplicate_area_ratio = 0.8        # 面积比例阈值，如果面积比例在这个范围内，认为可能是重复

# 圆内四边形过滤参数（混合策略）
min_vertices_for_conservative_filter = 2  # 保守过滤：中心在圆内时，至少需要多少个顶点在圆内
min_vertices_for_aggressive_filter = 3   # 积极过滤：中心在圆内时，至少需要多少个顶点在圆内
area_overlap_threshold = 0.7             # 面积重叠阈值：重叠比例超过此值认为四边形在圆内

# 多边形内四边形过滤参数（混合策略）
min_vertices_for_polygon_conservative_filter = 2  # 保守过滤：中心在多边形内时，至少需要多少个顶点在多边形内
min_vertices_for_polygon_aggressive_filter = 3   # 积极过滤：中心在多边形内时，至少需要多少个顶点在多边形内
polygon_overlap_threshold = 0.7                  # 面积重叠阈值：重叠比例超过此值认为四边形在多边形内

# 注意：上述多边形过滤参数同样适用于三角形过滤
# 对于三角形：min_vertices 的最大值为 3（三角形总共只有3个顶点）

# 边长数据参数
edge_history_size = 3   # 保存少量历史数据以备不时之需
edge_history = {}       # 存储边长历史数据的字典 {(shape_type, shape_position, edge_idx): [lengths...]}
circle_radius_history = {}  # 存储圆半径历史数据的字典 {(cx, cy): [radiuses...]}
# 形状位置跟踪参数
position_tolerance = 20  # 认为是同一个形状的位置容差（像素）
last_frame_shapes = {}   # 上一帧识别到的形状 {shape_type: [(cx, cy, area), ...]}
shape_tracking_data = {} # 形状跟踪数据，记录每个位置的形状历史 {(shape_type, cx, cy): frame_count}
# 顶点历史记录，用于跟踪变化
vertex_history = {}      # 顶点历史记录 {(shape_type, cx, cy): [vertices_list]}
vertex_history_size = 3  # 减少顶点历史记录数量，更关注最近的测量值
# 边长计算参数
use_instant_values = True  # 使用当前测量值而非平均值
# 形状超时参数
shape_timeout = 1       # 如果超过20帧未检测到，则清理该形状位置的跟踪数据

# ==================== 距离测量控制参数 ====================
enable_distance_measurement = True  # 是否启用距离测量功能

# ==================== 框内图形数据统计参数 ====================
enable_inner_shape_stats = True     # 是否启用框内图形数据统计
show_inner_shape_stats = True       # 是否在图像上显示框内图形统计信息

# ==================== 显示控制参数 ====================
show_edge_lengths = True            # 是否显示边长信息
show_shape_areas = False            # 是否显示形状面积信息

# ==================== 多边形拐点分析参数 ====================
enable_polygon_corner_analysis = True  # 是否启用多边形拐点分析功能
polygon_edge_overlap_threshold = 0.5   # 拐点对应边与多边形重叠阈值
enable_isolated_corner_detection = True  # 是否启用孤立拐点检测功能


# 距离测量相关变量
current_distance = 0.0           # 当前计算的距离
distance_history = []            # 距离历史记录，用于平滑
distance_history_size = 5        # 距离历史记录大小
second_largest_rect_info = None  # 第二大矩形信息

# 框内图形统计相关变量
inner_shapes_stats = {           # 框内图形统计数据
    'triangles': {
        'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
        'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0
    },
    'quadrilaterals': {
        'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
        'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0
    },
    'circles': {
        'count': 0, 'avg_radius': 0.0, 'total_radius': 0.0,
        'avg_radius_physical': 0.0, 'total_radius_physical': 0.0
    }
}

# 是否显示中间处理结果
show_debug = True
debug_view = 2  # 0: 原图, 1: 二值化, 2: 边缘, 3: 闭运算后

# 移除旧的show_area参数，现在使用show_shape_areas

# 计数变量，用于定时切换视图
view_switch_count = 0  # 视图切换计数器
frame_count = 0  # 总帧计数，用于预处理和形状跟踪

# 预处理相关参数
enable_preprocess = True  # 是否启用预处理筛选功能
preprocess_start_frame = 3  # 从第几帧开始启用预处理
max_rectangles = []  # 存储最大的两个矩形 [(contour, area, approx), ...]
preprocess_started = False  # 是否已经开始预处理
preprocess_stable_frames = 0  # 跟踪最大矩形保持稳定的帧数
preprocess_stable_threshold = 2  # 需要保持稳定的帧数阈值

# 缓存优化相关变量
cached_mask = None  # 缓存的掩码
cached_mask_valid = False  # 掩码是否有效
last_rectangles_centers = []  # 上一帧矩形的中心点，用于检测位置变化
rectangle_position_threshold = 10  # 矩形位置变化阈值（像素）

# ROI区域优化参数
roi_expand_pixels = 20  # 在最大框基础上向外扩展的像素数
use_roi_optimization = True  # 是否启用ROI区域优化
cached_roi_rect = None  # 缓存的ROI矩形区域 (x, y, w, h)
roi_valid = False  # ROI区域是否有效

while not app.need_exit():
    # 计时开始
    t_start = time.ticks_ms()

    # 读取图像
    img_maix = cam.read()
    
    # 将MaixPy图像转换为OpenCV格式
    t = time.ticks_ms()
    img_cv = image.image2cv(img_maix, ensure_bgr=True, copy=False)
    t_convert = time.ticks_ms() - t
    
    # 步骤1：图像二值化
    t = time.ticks_ms()
    gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

    # ROI优化：如果ROI有效且预处理完成，只处理ROI区域
    roi_offset_x, roi_offset_y = 0, 0  # ROI区域在原图中的偏移
    if (use_roi_optimization and roi_valid and
        enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
        preprocess_stable_frames >= preprocess_stable_threshold):

        x, y, w, h = cached_roi_rect
        # 裁剪灰度图到ROI区域
        gray_roi = gray[y:y+h, x:x+w]
        roi_offset_x, roi_offset_y = x, y

        # 对ROI区域进行二值化
        _, binary = cv2.threshold(gray_roi, binary_threshold, 255, cv2.THRESH_BINARY)
        print(f"ROI优化：只处理 {w}x{h} 区域，节省 {((gray.shape[0]*gray.shape[1] - w*h)/(gray.shape[0]*gray.shape[1])*100):.1f}% 计算量")
    else:
        # 处理整个图像
        _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
        gray_roi = gray

    t_binary = time.ticks_ms() - t
    
    # 步骤2：应用闭运算（膨胀后腐蚀）
    t = time.ticks_ms()
    if enable_closing:
        # 创建结构元素（核）
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        # 应用闭运算：先膨胀后腐蚀，填充小洞和连接相近区域
        binary_closed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    else:
        # 不需要copy，直接使用binary
        binary_closed = binary
    t_closing = time.ticks_ms() - t
    
    # 步骤3：边缘提取
    t = time.ticks_ms()
    blurred = cv2.GaussianBlur(binary_closed, (3, 3), 0)
    edges = cv2.Canny(blurred, canny_low, canny_high)
    t_edge = time.ticks_ms() - t
    
    # 步骤4：查找轮廓
    t = time.ticks_ms()
    contours, hierarchy = cv2.findContours(edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)

    # 收集内轮廓数据（用于孤立拐点特殊处理）
    inner_contours = []
    if hierarchy is not None:
        for i, contour in enumerate(contours):
            # hierarchy[0][i] = [next, previous, first_child, parent]
            # 如果有父轮廓，说明这是内轮廓
            if hierarchy[0][i][3] != -1:  # 有父轮廓
                inner_contours.append(contour)

    # 如果使用了ROI，需要将轮廓坐标转换回原图坐标系
    if (use_roi_optimization and roi_valid and
        enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
        preprocess_stable_frames >= preprocess_stable_threshold):

        # 将ROI区域内的轮廓坐标转换为原图坐标
        adjusted_contours = []
        for contour in contours:
            adjusted_contour = contour.copy()
            adjusted_contour[:, 0, 0] += roi_offset_x  # 调整x坐标
            adjusted_contour[:, 0, 1] += roi_offset_y  # 调整y坐标
            adjusted_contours.append(adjusted_contour)
        contours = adjusted_contours

    t_contour = time.ticks_ms() - t

    # 排序轮廓，从大到小
    contours = sorted(contours, key=cv2.contourArea, reverse=True)
    
    # 预处理步骤：识别最大的两个矩形并创建感兴趣区域
    if enable_preprocess and frame_count >= preprocess_start_frame:
        # 第一次运行预处理时，初始化最大矩形列表
        if not preprocess_started:
            print("开始预处理：识别最大的两个矩形")
            preprocess_started = True
            max_rectangles = []
            # 重置缓存
            cached_mask = None
            cached_mask_valid = False
            last_rectangles_centers = []
            # 重置ROI缓存
            cached_roi_rect = None
            roi_valid = False
        
        # 如果还没找到两个最大矩形，则尝试找出
        if len(max_rectangles) < 2:
            rect_found_this_frame = False
            for contour in contours:
                area = cv2.contourArea(contour)
                if area < min_area:
                    continue
                
                # 近似轮廓为多边形
                epsilon = epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # 如果是四边形
                if len(approx) == 4:
                    # 检查是否与已有的矩形重复
                    is_duplicate = False
                    for _, existing_area, existing_approx in max_rectangles:
                        # 计算中心点
                        M1 = cv2.moments(approx)
                        M2 = cv2.moments(existing_approx)
                        if M1["m00"] != 0 and M2["m00"] != 0:
                            cx1 = int(M1["m10"] / M1["m00"])
                            cy1 = int(M1["m01"] / M1["m00"])
                            cx2 = int(M2["m10"] / M2["m00"])
                            cy2 = int(M2["m01"] / M2["m00"])
                            
                            # 计算距离和面积比
                            distance = np.sqrt((cx1-cx2)**2 + (cy1-cy2)**2)
                            area_ratio = min(area, existing_area) / max(area, existing_area)
                            
                            # 判断是否重复
                            if distance < duplicate_distance_threshold and area_ratio > duplicate_area_ratio:
                                is_duplicate = True
                                break
                    
                    # 如果不是重复的且面积够大，加入最大矩形列表
                    if not is_duplicate:
                        max_rectangles.append((contour, area, approx))
                        max_rectangles = sorted(max_rectangles, key=lambda x: x[1], reverse=True)[:2]
                        rect_found_this_frame = True
                        print(f"找到新的大矩形，面积: {area}, 目前已找到 {len(max_rectangles)}/2")
                        
                        # 如果已经找到两个，打印确认信息
                        if len(max_rectangles) == 2:
                            print("已找到最大的两个矩形，开始筛选处理")
                            for i, (_, rect_area, _) in enumerate(max_rectangles):
                                print(f"矩形 {i+1} 面积: {rect_area}")
            
            # 如果本帧没有找到新矩形，重置稳定帧计数
            if not rect_found_this_frame and len(max_rectangles) < 2:
                preprocess_stable_frames = 0
                # 重置缓存，因为矩形配置可能发生变化
                cached_mask_valid = False
                roi_valid = False
        
        # 如果已经找到两个最大矩形，筛选轮廓
        if len(max_rectangles) == 2:
            # 检查矩形位置是否发生了显著变化
            current_centers = []
            rectangles_moved = False

            for _, _, approx in max_rectangles:
                M = cv2.moments(approx)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    current_centers.append((cx, cy))

            # 检查是否需要更新掩码
            need_update_mask = False
            if not cached_mask_valid or cached_mask is None:
                need_update_mask = True
                print("预处理：首次创建掩码")
            elif len(last_rectangles_centers) == len(current_centers):
                # 检查矩形位置是否发生显著变化
                for i, (cx, cy) in enumerate(current_centers):
                    if i < len(last_rectangles_centers):
                        last_cx, last_cy = last_rectangles_centers[i]
                        distance = np.sqrt((cx - last_cx)**2 + (cy - last_cy)**2)
                        if distance > rectangle_position_threshold:
                            need_update_mask = True
                            rectangles_moved = True
                            print(f"预处理：矩形{i+1}位置变化 {distance:.1f}像素，需要更新掩码")
                            break
            else:
                need_update_mask = True
                print("预处理：矩形数量变化，需要更新掩码")

            # 更新掩码（仅在需要时）
            if need_update_mask:
                try:
                    # 创建新的掩码
                    cached_mask = np.zeros_like(gray)

                    # 绘制两个大矩形到掩码上
                    for _, _, approx in max_rectangles:
                        cv2.drawContours(cached_mask, [approx], 0, 255, -1)

                    # 检查掩码是否有效
                    mask_pixels = np.sum(cached_mask > 0)
                    if mask_pixels < 100:
                        print(f"警告：生成的掩码像素太少 ({mask_pixels})，可能未正确识别矩形区域")
                        cached_mask_valid = False
                        preprocess_stable_frames = 0
                    else:
                        cached_mask_valid = True
                        last_rectangles_centers = current_centers.copy()
                        preprocess_stable_frames += 1
                        print(f"预处理：掩码更新成功，像素数: {mask_pixels}，掩码占比: {mask_pixels/(gray.shape[0]*gray.shape[1])*100:.2f}%")

                        # 计算ROI区域（如果启用ROI优化）
                        if use_roi_optimization:
                            try:
                                # 找到所有矩形点的边界
                                all_points = []
                                for _, _, approx in max_rectangles:
                                    for point in approx:
                                        all_points.append(point[0])

                                if all_points:
                                    all_points = np.array(all_points)
                                    min_x = max(0, np.min(all_points[:, 0]) - roi_expand_pixels)
                                    max_x = min(gray.shape[1], np.max(all_points[:, 0]) + roi_expand_pixels)
                                    min_y = max(0, np.min(all_points[:, 1]) - roi_expand_pixels)
                                    max_y = min(gray.shape[0], np.max(all_points[:, 1]) + roi_expand_pixels)

                                    cached_roi_rect = (int(min_x), int(min_y), int(max_x - min_x), int(max_y - min_y))
                                    roi_valid = True
                                    roi_area = (max_x - min_x) * (max_y - min_y)
                                    total_area = gray.shape[0] * gray.shape[1]
                                    roi_ratio = roi_area / total_area * 100
                                    print(f"预处理：ROI区域计算成功 ({min_x},{min_y},{max_x-min_x},{max_y-min_y})，占比: {roi_ratio:.1f}%")
                                else:
                                    roi_valid = False
                                    print("预处理：ROI计算失败，无有效矩形点")
                            except Exception as e:
                                print(f"ROI计算出错: {e}")
                                roi_valid = False

                except Exception as e:
                    print(f"掩码创建出错: {e}")
                    cached_mask_valid = False
                    preprocess_stable_frames = 0
                    # 重置ROI
                    roi_valid = False
            else:
                # 使用缓存的掩码，增加稳定帧计数
                preprocess_stable_frames += 1
                if preprocess_stable_frames % 10 == 0:  # 每10帧打印一次状态
                    mask_pixels = np.sum(cached_mask > 0)
                    print(f"预处理：使用缓存掩码，稳定度 {preprocess_stable_frames}，像素数: {mask_pixels}")

            # 应用掩码过滤（仅在掩码有效且稳定时）
            if cached_mask_valid and preprocess_stable_frames >= preprocess_stable_threshold:
                try:
                    # 筛选轮廓，只保留在掩码内的轮廓
                    filtered_contours = []
                    for contour in contours:
                        # 计算轮廓中心点
                        M = cv2.moments(contour)
                        if M["m00"] != 0:
                            cx = int(M["m10"] / M["m00"])
                            cy = int(M["m01"] / M["m00"])

                            # 检查中心点是否在掩码内
                            if 0 <= cy < cached_mask.shape[0] and 0 <= cx < cached_mask.shape[1] and cached_mask[cy, cx] > 0:
                                filtered_contours.append(contour)
                        else:
                            # 如果不能计算中心点，检查轮廓上的点是否在掩码内
                            inside_points = 0
                            for point in contour:
                                px, py = point[0]
                                if 0 <= py < cached_mask.shape[0] and 0 <= px < cached_mask.shape[1] and cached_mask[py, px] > 0:
                                    inside_points += 1
                            # 如果至少有一半的点在掩码内，认为这个轮廓在区域内
                            if inside_points >= len(contour) / 2:
                                filtered_contours.append(contour)

                    # 更新轮廓列表
                    original_count = len(contours)
                    contours = filtered_contours
                    if original_count != len(contours):
                        print(f"预处理：从 {original_count} 个轮廓筛选出 {len(contours)} 个位于主矩形内的轮廓")

                except Exception as e:
                    print(f"掩码应用出错: {e}")
            else:
                if not cached_mask_valid:
                    print("预处理：掩码无效，跳过过滤")
                else:
                    print(f"预处理：等待矩形区域稳定 ({preprocess_stable_frames}/{preprocess_stable_threshold})")
        else:
            # 如果还没有找到两个矩形，重置稳定帧计数
            preprocess_stable_frames = 0
            # 重置缓存
            cached_mask_valid = False
            roi_valid = False
    
    # 选择显示哪个视图
    if show_debug:
        if debug_view == 0:
            img_result = image.cv2image(img_cv, bgr=True, copy=False)
        elif debug_view == 1:
            # 将二值图像转换为可显示的MaixPy图像
            # 如果使用了ROI，需要将ROI区域的结果放回完整图像中
            if (use_roi_optimization and roi_valid and
                enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
                preprocess_stable_frames >= preprocess_stable_threshold):
                # 创建完整尺寸的二值图像
                full_binary = np.zeros_like(gray)
                x, y, w, h = cached_roi_rect
                # 确保binary的尺寸与ROI区域匹配
                if binary.shape[0] == h and binary.shape[1] == w:
                    full_binary[y:y+h, x:x+w] = binary
                else:
                    print(f"警告：binary尺寸 {binary.shape} 与ROI尺寸 ({h},{w}) 不匹配")
                    # 回退到直接使用binary
                    full_binary = binary if binary.shape == gray.shape else np.zeros_like(gray)
                binary_colored = np.stack([full_binary, full_binary, full_binary], axis=2)
            else:
                binary_colored = np.stack([binary, binary, binary], axis=2)
            img_result = image.cv2image(binary_colored)
        elif debug_view == 2:
            # 将边缘图像转换为可显示的MaixPy图像
            # 如果使用了ROI，需要将ROI区域的结果放回完整图像中
            if (use_roi_optimization and roi_valid and
                enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
                preprocess_stable_frames >= preprocess_stable_threshold):
                # 创建完整尺寸的边缘图像
                full_edges = np.zeros_like(gray)
                x, y, w, h = cached_roi_rect
                # 确保edges的尺寸与ROI区域匹配
                if edges.shape[0] == h and edges.shape[1] == w:
                    full_edges[y:y+h, x:x+w] = edges
                else:
                    print(f"警告：edges尺寸 {edges.shape} 与ROI尺寸 ({h},{w}) 不匹配")
                    # 回退到直接使用edges
                    full_edges = edges if edges.shape == gray.shape else np.zeros_like(gray)
                edges_colored = np.stack([full_edges, full_edges, full_edges], axis=2)
            else:
                edges_colored = np.stack([edges, edges, edges], axis=2)
            img_result = image.cv2image(edges_colored)
        else:  # debug_view == 3
            # 将闭运算后的二值图像转换为可显示的MaixPy图像
            # 如果使用了ROI，需要将ROI区域的结果放回完整图像中
            if (use_roi_optimization and roi_valid and
                enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
                preprocess_stable_frames >= preprocess_stable_threshold):
                # 创建完整尺寸的闭运算图像
                full_binary_closed = np.zeros_like(gray)
                x, y, w, h = cached_roi_rect
                # 确保binary_closed的尺寸与ROI区域匹配
                if binary_closed.shape[0] == h and binary_closed.shape[1] == w:
                    full_binary_closed[y:y+h, x:x+w] = binary_closed
                else:
                    print(f"警告：binary_closed尺寸 {binary_closed.shape} 与ROI尺寸 ({h},{w}) 不匹配")
                    # 回退到直接使用binary_closed
                    full_binary_closed = binary_closed if binary_closed.shape == gray.shape else np.zeros_like(gray)
                closed_colored = np.stack([full_binary_closed, full_binary_closed, full_binary_closed], axis=2)
            else:
                closed_colored = np.stack([binary_closed, binary_closed, binary_closed], axis=2)
            img_result = image.cv2image(closed_colored)
    else:
        img_result = image.cv2image(img_cv, bgr=True, copy=False)
    
    # 步骤5：识别形状
    triangle_count = 0
    quadrilateral_count = 0
    circle_count = 0       # 添加圆形计数
    polygon_count = 0      # 添加多边形计数
    min_detected_area = float('inf')  # 跟踪检测到的最小面积

    # 重置框内图形统计数据（每帧开始时重置，为本帧统计做准备）
    if enable_inner_shape_stats:
        reset_inner_shape_stats(inner_shapes_stats)

    # 用于跟踪已检测到的形状，防止重复
    detected_shapes = []  # 将存储 (cx, cy, area, vertices)
    
    # 先识别所有四边形
    quadrilaterals = []
    
    for contour in contours:
        # 计算轮廓面积
        area = cv2.contourArea(contour)
        
        # 过滤掉太小的轮廓
        if area < min_area:
            continue
            
        # 近似轮廓为多边形
        epsilon = epsilon_factor * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 根据拐点数量识别形状
        num_vertices = len(approx)
        
        # 找出所有四边形并保存
        if num_vertices == 4:
            # 计算四边形的中心点
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                
                # 检查是否是最大的两个矩形之一
                is_max_rect = False
                if enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
                    for _, max_area, max_approx in max_rectangles:
                        # 计算最大矩形的中心点
                        M_max = cv2.moments(max_approx)
                        if M_max["m00"] != 0:
                            cx_max = int(M_max["m10"] / M_max["m00"])
                            cy_max = int(M_max["m01"] / M_max["m00"])
                            
                            # 计算距离和面积比
                            distance = calculate_distance((cx, cy), (cx_max, cy_max))
                            area_ratio = calculate_area_ratio(area, max_area)
                            
                            # 判断是否是同一个矩形
                            if distance < duplicate_distance_threshold and area_ratio > duplicate_area_ratio:
                                is_max_rect = True
                                break
                
                # 检查是否是重复的四边形
                is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, 4,
                                                duplicate_distance_threshold, duplicate_area_ratio)
                
                # 如果不是重复的，则添加到候选四边形列表（延迟详细处理）
                if not is_duplicate:
                    # 存储四边形信息，包含处理所需的所有数据
                    quad_info = {
                        'approx': approx,
                        'cx': cx,
                        'cy': cy,
                        'area': area,
                        'is_max_rect': is_max_rect
                    }
                    quadrilaterals.append(quad_info)
                    detected_shapes.append((cx, cy, area, num_vertices))
                    quadrilateral_count += 1
    
    # 存储检测到的圆形信息，用于后续过滤四边形
    detected_circles = []  # 存储 (cx, cy, radius) 信息

    # 存储检测到的多边形信息，用于后续过滤四边形和三角形
    detected_polygons = []  # 存储 (cx, cy, approx) 信息

    # 🔥 新增：存储检测到的三角形信息，用于后续过滤
    detected_triangles = []  # 存储 (cx, cy, area, approx) 信息

    # 🔥 新增：收集四边形区域内的精细多边形用于过滤
    if quadrilaterals:  # 如果有四边形，先收集精细多边形
        fine_polygons = collect_fine_polygons_from_quads(
            quadrilaterals, img_cv, edges, min_area,
            enable_preprocess, preprocess_started, max_rectangles,
            preprocess_stable_frames, preprocess_stable_threshold
        )
        detected_polygons.extend(fine_polygons)
        # 简化调试输出
        pass

    # 然后处理所有轮廓，识别三角形、圆形和多边形
    for contour in contours:
        # 计算轮廓面积
        area = cv2.contourArea(contour)
        
        # 过滤掉太小的轮廓
        if area < min_area:
            continue
            
        # 近似轮廓为多边形
        epsilon = epsilon_factor * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 根据拐点数量识别形状
        num_vertices = len(approx)
        
        # 计算周长
        perimeter = cv2.arcLength(contour, True)
        
        # 计算轮廓的中心点
        M = cv2.moments(contour)
        if M["m00"] == 0:
            continue  # 跳过无法计算中心点的轮廓
            
        cx = int(M["m10"] / M["m00"])
        cy = int(M["m01"] / M["m00"])
        
        # 检查这个轮廓是否在任何一个四边形内部
        is_inside_quad = False

        if (enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold):
            # 预处理完成后，检查是否在最大矩形内
            for _, _, max_approx in max_rectangles:
                if cv2.pointPolygonTest(max_approx, (int(cx), int(cy)), False) >= 0:
                    is_inside_quad = True
                    break
        else:
            # 预处理未完成，检查是否在任何四边形内
            for quad in quadrilaterals:
                # 使用OpenCV的pointPolygonTest检查点是否在多边形内
                # 如果返回值大于0，则点在多边形内部
                if cv2.pointPolygonTest(quad['approx'], (int(cx), int(cy)), False) > 0:
                    is_inside_quad = True
                    break
                
        # 只处理在四边形内部的形状
        if not is_inside_quad:
            continue
        
        # 更新检测到的最小面积
        if area < min_detected_area:
            min_detected_area = area
            
        # 识别圆形
        # 圆的特征是：面积与周长的关系接近于 4*π*area = perimeter²
        circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
        
        # 先检查是否是圆形（圆度接近1，表示非常接近圆形）
        if circularity > 0.85:  # 圆度阈值，可以根据实际情况调整
            # 检查是否是重复的圆形
            is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, -1,
                                            duplicate_distance_threshold, duplicate_area_ratio)

            # 如果是重复的，跳过
            if is_duplicate:
                continue
            
            shape = "Circle"  # 圆形
            color = image.COLOR_BLUE
            circle_count += 1
            
            # 添加到已检测形状列表，使用-1表示圆形
            detected_shapes.append((cx, cy, area, -1))

            # 计算圆形半径并记录，用于后续过滤四边形
            radius = int(np.sqrt(area / np.pi))
            detected_circles.append((cx, cy, radius))
            
        # 如果为多边形（5个或更多拐点）
        elif num_vertices >= 5:
            # 检查是否是重复的多边形
            is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, num_vertices,
                                            duplicate_distance_threshold, duplicate_area_ratio)

            # 如果是重复的，跳过
            if is_duplicate:
                continue

            shape = f"Polygon({num_vertices})"  # 多边形，显示顶点数
            color = image.COLOR_PURPLE  # 使用紫色表示多边形
            polygon_count += 1

            # 添加到已检测形状列表
            detected_shapes.append((cx, cy, area, num_vertices))

            # 存储多边形信息，用于后续过滤四边形
            detected_polygons.append((cx, cy, approx))
            
            # 🔥 新增：处理多边形拐点分析
            if (enable_polygon_corner_analysis and enable_preprocess and preprocess_started and len(max_rectangles) == 2 and 
                preprocess_stable_frames >= preprocess_stable_threshold):
                
                # 获取参考物理长度
                reference_pixels = get_second_largest_rect_longest_edge(max_rectangles)
                
                if reference_pixels > 0:
                    # 处理多边形拐点并计算最小边长
                    min_edge_pixels, min_edge_physical, corner_marks, min_edge_indices = process_polygon_corners_and_find_min_edge(
                        approx, reference_pixels, second_largest_rect_physical_length,
                        polygon_edge_overlap_threshold, enable_isolated_corner_detection, inner_contours
                    )
                    
                    # 提取顶点坐标列表用于绘制
                    vertices = [tuple(pt[0]) for pt in approx]
                    
                    # 在图像上绘制拐点分析结果
                    if min_edge_physical > 0:
                        draw_polygon_corner_analysis(img_result, vertices, corner_marks, min_edge_indices,
                                                   min_edge_physical)
                        # 简化多边形分析输出

        # 如果为三角形（3个拐点）
        elif num_vertices == 3:
            # 检查是否是重复的三角形
            is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, 3,
                                            duplicate_distance_threshold, duplicate_area_ratio)

            # 如果是重复的，跳过
            if is_duplicate:
                continue

            # 🔥 修改：将三角形信息存储到候选列表中，延迟处理
            triangle_info = {
                'approx': approx,
                'cx': cx,
                'cy': cy,
                'area': area
            }
            detected_triangles.append(triangle_info)
            detected_shapes.append((cx, cy, area, num_vertices))
            triangle_count += 1
            continue  # 跳过后面的形状处理，三角形将在过滤阶段处理

        else:
            # 不是三角形也不是圆形，跳过
            continue
        
        # 在控制台打印形状信息（只在预处理完成后打印）
        if (enable_preprocess and len(max_rectangles) == 2 and 
            frame_count >= preprocess_start_frame + preprocess_stable_threshold):
            # 简化输出，不打印详细信息
            pass
        
        # 在图像上标记识别结果
        img_result.draw_string(cx-20, cy, shape, color)
        # 如果启用，显示轮廓面积
        if show_shape_areas:
            area_text = f"A:{int(area)}"
            img_result.draw_string(cx-20, cy+15, area_text, color)
        
        # 如果是三角形，处理和显示边长
        if shape == "Triangle":
            # 提取顶点坐标列表
            vertices = [tuple(pt[0]) for pt in approx]

            # 查找匹配的形状位置并更新跟踪数据
            shape_position = find_matching_shape_position(cx, cy, "Triangle", shape_tracking_data, position_tolerance)
            shape_position, cx, cy, vertices = update_shape_tracking(
                shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
                vertex_history, vertex_history_size, "Triangle"
            )

            # 记录该三角形到当前帧三角形列表
            if "Triangle" not in last_frame_shapes:
                last_frame_shapes["Triangle"] = []
            last_frame_shapes["Triangle"].append((cx, cy, area))

            # 更新框内图形统计数据（只在预处理完成后统计）
            if enable_inner_shape_stats and enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
                update_inner_shape_stats("Triangle", vertices=vertices, inner_shapes_stats=inner_shapes_stats,
                                        max_rectangles=max_rectangles, second_largest_rect_physical_length=second_largest_rect_physical_length)

            # 画出轮廓和拐点，并显示边长
            draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                                  edge_history, edge_history_size, use_instant_values)

        # 如果是多边形，进行完整处理
        elif shape.startswith("Polygon"):
            # 提取顶点坐标列表
            vertices = [tuple(pt[0]) for pt in approx]

            # 查找匹配的形状位置并更新跟踪数据
            shape_position = find_matching_shape_position(cx, cy, "Polygon", shape_tracking_data, position_tolerance)
            shape_position, cx, cy, vertices = update_shape_tracking(
                shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
                vertex_history, vertex_history_size, "Polygon"
            )

            # 记录该多边形到当前帧多边形列表
            if "Polygon" not in last_frame_shapes:
                last_frame_shapes["Polygon"] = []
            last_frame_shapes["Polygon"].append((cx, cy, area))

            # 更新框内图形统计数据（只在预处理完成后统计）
            if enable_inner_shape_stats and enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
                update_inner_shape_stats("Polygon", vertices=vertices, inner_shapes_stats=inner_shapes_stats,
                                        max_rectangles=max_rectangles, second_largest_rect_physical_length=second_largest_rect_physical_length)

            # 🔥 新增：处理多边形拐点分析
            analysis_completed = False
            if (enable_polygon_corner_analysis and enable_preprocess and preprocess_started and len(max_rectangles) == 2 and 
                preprocess_stable_frames >= preprocess_stable_threshold):
                
                # 获取参考物理长度
                reference_pixels = get_second_largest_rect_longest_edge(max_rectangles)
                
                if reference_pixels > 0:
                    # 处理多边形拐点并计算最小边长
                    min_edge_pixels, min_edge_physical, corner_marks, min_edge_indices = process_polygon_corners_and_find_min_edge(
                        approx, reference_pixels, second_largest_rect_physical_length,
                        polygon_edge_overlap_threshold, enable_isolated_corner_detection, inner_contours
                    )
                    
                    # 验证结果的合理性
                    is_valid, validation_msg = validate_polygon_analysis_result(
                        vertices, corner_marks, min_edge_indices, min_edge_pixels
                    )
                    # 简化验证输出
                    
                    # 在图像上绘制拐点分析结果
                    if is_valid and min_edge_physical > 0:
                        draw_polygon_corner_analysis(img_result, vertices, corner_marks, min_edge_indices,
                                                   min_edge_physical)
                        analysis_completed = True
                        # 简化多边形分析输出
            
            # 如果拐点分析未完成，画出常规的轮廓和拐点
            if not analysis_completed:
                # 多边形只画出轮廓和拐点，不计算边长（因为边数不固定）
                for i in range(len(vertices)):
                    pt1 = vertices[i]
                    pt2 = vertices[(i+1) % len(vertices)]  # 连接到下一个点，形成闭环

                    # 绘制线段（边）
                    img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], color, 2)

                    # 绘制拐点
                    img_result.draw_circle(pt1[0], pt1[1], 3, color, thickness=-1)  # 实心圆

        # 如果是圆形，计算和显示半径
        elif shape == "Circle":
            # 查找匹配的形状位置并更新跟踪数据
            shape_position = find_matching_shape_position(cx, cy, "Circle", shape_tracking_data, position_tolerance)
            shape_position, cx, cy, _ = update_shape_tracking(
                shape_position, cx, cy, None, frame_count, shape_tracking_data,
                vertex_history, vertex_history_size, "Circle"
            )
            
            # 记录该圆形到当前帧圆形列表
            if "Circle" not in last_frame_shapes:
                last_frame_shapes["Circle"] = []
            last_frame_shapes["Circle"].append((cx, cy, area))

            # 计算等效半径（基于面积的圆半径）
            radius = int(np.sqrt(area / np.pi))

            # 使用形状位置作为键，确保同一位置的圆形共享历史数据
            if shape_position not in circle_radius_history:
                circle_radius_history[shape_position] = []

            # 添加当前半径到历史记录
            circle_radius_history[shape_position].append(radius)

            # 保持历史记录在指定大小内
            if len(circle_radius_history[shape_position]) > edge_history_size:
                circle_radius_history[shape_position].pop(0)

            # 使用改进的平均值计算方法计算平均半径
            display_radius = get_value_from_history(circle_radius_history[shape_position], use_instant_values)

            # 更新框内图形统计数据（只在预处理完成后统计）
            if enable_inner_shape_stats and enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
                update_inner_shape_stats("Circle", radius=display_radius, inner_shapes_stats=inner_shapes_stats,
                                        max_rectangles=max_rectangles, second_largest_rect_physical_length=second_largest_rect_physical_length)

            # 绘制圆形轮廓
            img_result.draw_circle(cx, cy, display_radius, color, thickness=2)

            # 绘制圆心
            img_result.draw_circle(cx, cy, 3, color, thickness=-1)

            # 显示半径信息（根据参数控制）
            if show_edge_lengths:
                radius_text = f"R:{display_radius}"
                img_result.draw_string(cx + 5, cy, radius_text, image.COLOR_RED)

            # 在控制台打印半径信息（简化输出）
            if (enable_preprocess and len(max_rectangles) == 2 and 
                frame_count >= preprocess_start_frame + preprocess_stable_threshold):
                # 简化输出，不打印详细信息
                pass

        # 如果是多边形，处理和显示
        elif shape.startswith("Polygon"):
            # 提取顶点坐标列表
            vertices = [tuple(pt[0]) for pt in approx]

            # 查找匹配的形状位置并更新跟踪数据
            shape_position = find_matching_shape_position(cx, cy, "Polygon", shape_tracking_data, position_tolerance)
            shape_position, cx, cy, vertices = update_shape_tracking(
                shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
                vertex_history, vertex_history_size, "Polygon"
            )

            # 记录该多边形到当前帧多边形列表
            if "Polygon" not in last_frame_shapes:
                last_frame_shapes["Polygon"] = []
            last_frame_shapes["Polygon"].append((cx, cy, area))

            # 画出轮廓和拐点（多边形不计算边长，只显示形状）
            for i in range(len(vertices)):
                pt1 = vertices[i]
                pt2 = vertices[(i+1) % len(vertices)]  # 连接到下一个点，形成闭环

                # 绘制线段（边）
                img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], color, 2)

                # 绘制拐点
                img_result.draw_circle(pt1[0], pt1[1], 3, color, thickness=-1)  # 实心圆

    # 过滤在圆形和多边形内部的四边形并处理保留的四边形
    # 简化调试输出

    if detected_circles or detected_polygons:
        retained_quads = []
        removed_quad_count = 0
        filtered_quad_centers = set()  # 收集被过滤四边形的中心点
        filtered_quad_positions = []   # 收集需要清理的四边形位置信息

        for quad_info in quadrilaterals:
            quad_approx = quad_info['approx']
            quad_cx = quad_info['cx']
            quad_cy = quad_info['cy']
            quad_area = quad_info['area']
            is_max_rect = quad_info['is_max_rect']

            # 使用综合策略检查四边形是否在任何圆形或多边形内部
            is_inside_shape = False
            filter_reason = ""

            # 检查是否在圆形内部
            for circle_cx, circle_cy, circle_radius in detected_circles:
                inside, reason = is_quad_inside_circle_comprehensive(
                    quad_approx, circle_cx, circle_cy, circle_radius,
                    min_vertices_for_conservative_filter, min_vertices_for_aggressive_filter,
                    area_overlap_threshold
                )
                if inside:
                    is_inside_shape = True
                    filter_reason = reason
                    # 简化过滤输出
                    break

            # 如果不在圆形内部，检查是否在多边形内部
            if not is_inside_shape:
                for polygon_cx, polygon_cy, polygon_approx in detected_polygons:
                    inside, reason = is_quad_inside_polygon_comprehensive(
                        quad_approx, polygon_approx,
                        min_vertices_for_polygon_conservative_filter, min_vertices_for_polygon_aggressive_filter,
                        polygon_overlap_threshold
                    )
                    if inside:
                        is_inside_shape = True
                        filter_reason = reason
                        # 简化过滤输出
                        break

            # 如果不在任何圆形或多边形内部，处理这个四边形
            if not is_inside_shape:
                # 对保留的四边形进行完整处理
                process_retained_quadrilateral(
                    quad_approx, quad_cx, quad_cy, quad_area, is_max_rect, frame_count,
                    shape_tracking_data, vertex_history, vertex_history_size,
                    position_tolerance, last_frame_shapes, img_result, edge_history,
                    edge_history_size, use_instant_values, max_rectangles,
                    enable_distance_measurement, calibration_pixels,
                    calibration_distance, distance_history, distance_history_size,
                    enable_inner_shape_stats, inner_shapes_stats, img_cv, edges, min_area,
                    detected_shapes, duplicate_distance_threshold, duplicate_area_ratio,
                    enable_preprocess, preprocess_started, preprocess_stable_frames,
                    preprocess_stable_threshold
                )
                retained_quads.append(quad_approx)
            else:
                # 收集被过滤四边形的中心点信息（不进行任何处理）
                filtered_quad_centers.add((quad_cx, quad_cy))
                quad_position = ("Quad", quad_cx, quad_cy)
                filtered_quad_positions.append(quad_position)
                removed_quad_count += 1

        # 更新四边形列表（不需要更新计数，因为quadrilateral_count在检测时已经正确计算）
        quadrilaterals = retained_quads
    else:
        # 没有检测到圆形，处理所有四边形
        retained_quads = []
        for quad_info in quadrilaterals:
            quad_approx = quad_info['approx']
            quad_cx = quad_info['cx']
            quad_cy = quad_info['cy']
            quad_area = quad_info['area']
            is_max_rect = quad_info['is_max_rect']

            # 对所有四边形进行完整处理
            process_retained_quadrilateral(
                quad_approx, quad_cx, quad_cy, quad_area, is_max_rect, frame_count,
                shape_tracking_data, vertex_history, vertex_history_size,
                position_tolerance, last_frame_shapes, img_result, edge_history,
                edge_history_size, use_instant_values, max_rectangles,
                enable_distance_measurement, calibration_pixels,
                calibration_distance, distance_history, distance_history_size,
                enable_inner_shape_stats, inner_shapes_stats, img_cv, edges, min_area,
                detected_shapes, duplicate_distance_threshold, duplicate_area_ratio,
                enable_preprocess, preprocess_started, preprocess_stable_frames,
                preprocess_stable_threshold
            )
            retained_quads.append(quad_approx)

        quadrilaterals = retained_quads
        filtered_quad_centers = set()
        filtered_quad_positions = []
        removed_quad_count = 0

    if removed_quad_count > 0:
        # 从detected_shapes中移除被过滤的四边形
        filtered_detected_shapes = []
        for detected_cx, detected_cy, detected_area, detected_vertices in detected_shapes:
            if detected_vertices == 4:  # 四边形
                # 检查这个四边形的中心点是否在被过滤的集合中
                if (detected_cx, detected_cy) not in filtered_quad_centers:
                    filtered_detected_shapes.append((detected_cx, detected_cy, detected_area, detected_vertices))
            else:
                # 非四边形，保留
                filtered_detected_shapes.append((detected_cx, detected_cy, detected_area, detected_vertices))

        detected_shapes = filtered_detected_shapes

    # 同时需要从last_frame_shapes中移除被过滤的四边形
    if "Quad" in last_frame_shapes and removed_quad_count > 0:
        filtered_quad_shapes = []
        for quad_cx, quad_cy, quad_area in last_frame_shapes["Quad"]:
            # 检查这个四边形的中心点是否在被过滤的集合中
            if (quad_cx, quad_cy) not in filtered_quad_centers:
                filtered_quad_shapes.append((quad_cx, quad_cy, quad_area))

        last_frame_shapes["Quad"] = filtered_quad_shapes

    # 清理被过滤四边形的所有跟踪数据
    if filtered_quad_positions:
        for quad_pos in filtered_quad_positions:
            try:
                # 清理形状跟踪数据
                if quad_pos in shape_tracking_data:
                    shape_tracking_data.pop(quad_pos, None)
                    # 简化清理输出

                # 清理顶点历史记录
                if quad_pos in vertex_history:
                    vertex_history.pop(quad_pos, None)

                # 清理边长历史记录
                edge_keys_to_remove = []
                for key in edge_history.keys():
                    if isinstance(key, tuple) and len(key) >= 1 and key[0] == quad_pos:
                        edge_keys_to_remove.append(key)

                for key in edge_keys_to_remove:
                    edge_history.pop(key, None)

            except Exception as e:
                # 简化错误输出
                pass

    # 简化过滤总结输出
    if removed_quad_count > 0:
        print(f"过滤了 {removed_quad_count} 个四边形")

    if detected_polygons and detected_triangles:
        retained_triangles = []
        removed_triangle_count = 0
        filtered_triangle_centers = set()  # 收集被过滤三角形的中心点
        filtered_triangle_positions = []   # 收集需要清理的三角形位置信息

        for triangle_info in detected_triangles:
            triangle_approx = triangle_info['approx']
            triangle_cx = triangle_info['cx']
            triangle_cy = triangle_info['cy']
            triangle_area = triangle_info['area']

            # 使用综合策略检查三角形是否在任何多边形内部
            is_inside_polygon = False
            filter_reason = ""

            # 检查是否在多边形内部
            for polygon_cx, polygon_cy, polygon_approx in detected_polygons:
                inside, reason = is_triangle_inside_polygon_comprehensive(
                    triangle_approx, polygon_approx,
                    min_vertices_for_polygon_conservative_filter, min_vertices_for_polygon_aggressive_filter,
                    polygon_overlap_threshold
                )
                if inside:
                    is_inside_polygon = True
                    filter_reason = reason
                    # 简化过滤输出
                    break

            # 如果不在任何多边形内部，处理这个三角形
            if not is_inside_polygon:
                # 对保留的三角形进行完整处理
                shape = "Triangle"  # 三角形
                color = image.COLOR_RED

                # 在控制台打印形状信息（只在预处理完成后打印）
                if (enable_preprocess and len(max_rectangles) == 2 and 
                    frame_count >= preprocess_start_frame + preprocess_stable_threshold):
                    # 简化输出，不打印详细信息
                    pass

                # 在图像上标记识别结果
                img_result.draw_string(triangle_cx-20, triangle_cy, shape, color)
                # 如果启用，显示轮廓面积
                if show_shape_areas:
                    area_text = f"A:{int(triangle_area)}"
                    img_result.draw_string(triangle_cx-20, triangle_cy+15, area_text, color)

                # 提取顶点坐标列表
                vertices = [tuple(pt[0]) for pt in triangle_approx]

                # 查找匹配的形状位置并更新跟踪数据
                shape_position = find_matching_shape_position(triangle_cx, triangle_cy, "Triangle", shape_tracking_data, position_tolerance)
                shape_position, triangle_cx, triangle_cy, vertices = update_shape_tracking(
                    shape_position, triangle_cx, triangle_cy, vertices, frame_count, shape_tracking_data,
                    vertex_history, vertex_history_size, "Triangle"
                )

                # 记录该三角形到当前帧三角形列表
                if "Triangle" not in last_frame_shapes:
                    last_frame_shapes["Triangle"] = []
                last_frame_shapes["Triangle"].append((triangle_cx, triangle_cy, triangle_area))

                # 更新框内图形统计数据（只在预处理完成后统计）
                if enable_inner_shape_stats and enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
                    update_inner_shape_stats("Triangle", vertices=vertices, inner_shapes_stats=inner_shapes_stats,
                                            max_rectangles=max_rectangles, second_largest_rect_physical_length=second_largest_rect_physical_length)

                # 画出轮廓和拐点，并显示边长
                draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                                      edge_history, edge_history_size, use_instant_values)

                retained_triangles.append(triangle_info)
            else:
                # 收集被过滤三角形的中心点信息（不进行任何处理）
                filtered_triangle_centers.add((triangle_cx, triangle_cy))
                triangle_position = ("Triangle", triangle_cx, triangle_cy)
                filtered_triangle_positions.append(triangle_position)
                removed_triangle_count += 1

        # 更新三角形列表（不需要更新计数，因为triangle_count在检测时已经正确计算）
        detected_triangles = retained_triangles
    else:
        # 没有检测到多边形，或者没有三角形，处理所有三角形
        for triangle_info in detected_triangles:
            triangle_approx = triangle_info['approx']
            triangle_cx = triangle_info['cx']
            triangle_cy = triangle_info['cy']
            triangle_area = triangle_info['area']

            # 对所有三角形进行完整处理
            shape = "Triangle"  # 三角形
            color = image.COLOR_RED

            # 在控制台打印形状信息（只在预处理完成后打印）
            if (enable_preprocess and len(max_rectangles) == 2 and 
                frame_count >= preprocess_start_frame + preprocess_stable_threshold):
                # 简化输出，不打印详细信息
                pass

            # 在图像上标记识别结果
            img_result.draw_string(triangle_cx-20, triangle_cy, shape, color)
            # 如果启用，显示轮廓面积
            if show_shape_areas:
                area_text = f"A:{int(triangle_area)}"
                img_result.draw_string(triangle_cx-20, triangle_cy+15, area_text, color)

            # 提取顶点坐标列表
            vertices = [tuple(pt[0]) for pt in triangle_approx]

            # 查找匹配的形状位置并更新跟踪数据
            shape_position = find_matching_shape_position(triangle_cx, triangle_cy, "Triangle", shape_tracking_data, position_tolerance)
            shape_position, triangle_cx, triangle_cy, vertices = update_shape_tracking(
                shape_position, triangle_cx, triangle_cy, vertices, frame_count, shape_tracking_data,
                vertex_history, vertex_history_size, "Triangle"
            )

            # 记录该三角形到当前帧三角形列表
            if "Triangle" not in last_frame_shapes:
                last_frame_shapes["Triangle"] = []
            last_frame_shapes["Triangle"].append((triangle_cx, triangle_cy, triangle_area))

            # 更新框内图形统计数据（只在预处理完成后统计）
            if enable_inner_shape_stats and enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
                update_inner_shape_stats("Triangle", vertices=vertices, inner_shapes_stats=inner_shapes_stats,
                                        max_rectangles=max_rectangles, second_largest_rect_physical_length=second_largest_rect_physical_length)

            # 画出轮廓和拐点，并显示边长
            draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                                  edge_history, edge_history_size, use_instant_values)

        # 没有过滤，所以初始化这些变量
        filtered_triangle_centers = set()
        filtered_triangle_positions = []
        removed_triangle_count = 0

    # 处理被过滤三角形的清理工作
    if removed_triangle_count > 0:
        # 从detected_shapes中移除被过滤的三角形
        filtered_detected_shapes = []
        for detected_cx, detected_cy, detected_area, detected_vertices in detected_shapes:
            if detected_vertices == 3:  # 三角形
                # 检查这个三角形的中心点是否在被过滤的集合中
                if (detected_cx, detected_cy) not in filtered_triangle_centers:
                    filtered_detected_shapes.append((detected_cx, detected_cy, detected_area, detected_vertices))
            else:
                # 非三角形，保留
                filtered_detected_shapes.append((detected_cx, detected_cy, detected_area, detected_vertices))

        detected_shapes = filtered_detected_shapes

        # 同时需要从last_frame_shapes中移除被过滤的三角形
        if "Triangle" in last_frame_shapes:
            filtered_triangle_shapes = []
            for tri_cx, tri_cy, tri_area in last_frame_shapes["Triangle"]:
                # 检查这个三角形的中心点是否在被过滤的集合中
                if (tri_cx, tri_cy) not in filtered_triangle_centers:
                    filtered_triangle_shapes.append((tri_cx, tri_cy, tri_area))

            last_frame_shapes["Triangle"] = filtered_triangle_shapes

        # 清理被过滤三角形的所有跟踪数据
        if filtered_triangle_positions:
            for triangle_pos in filtered_triangle_positions:
                try:
                    # 清理形状跟踪数据
                    if triangle_pos in shape_tracking_data:
                        shape_tracking_data.pop(triangle_pos, None)
                        # 简化清理输出

                    # 清理顶点历史记录
                    if triangle_pos in vertex_history:
                        vertex_history.pop(triangle_pos, None)

                    # 清理边长历史记录
                    edge_keys_to_remove = []
                    for key in edge_history.keys():
                        if isinstance(key, tuple) and len(key) >= 1 and key[0] == triangle_pos:
                            edge_keys_to_remove.append(key)

                    for key in edge_keys_to_remove:
                        edge_history.pop(key, None)

                except Exception as e:
                    # 简化错误输出
                    pass

        print(f"过滤了 {removed_triangle_count} 个三角形")

    # 打印帧分隔符和整理后的统计信息
    if enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
        y_offset = 8  # 起始位置稍微下移

        # 显示ROI优化状态
        if use_roi_optimization and roi_valid:
            x, y, w, h = cached_roi_rect
            total_area = gray.shape[0] * gray.shape[1]
            roi_area_percent = (w*h)/total_area*100 if total_area > 0 else 0
            roi_info = f"ROI: {w}x{h} ({roi_area_percent:.0f}%)"
            img_result.draw_string(8, y_offset, roi_info, image.COLOR_GREEN)
            y_offset += 22

        # 显示距离测量信息
        if enable_distance_measurement and current_distance > 0:
            distance_info = f"Distance: {current_distance:.1f}cm"
            img_result.draw_string(8, y_offset, distance_info, image.COLOR_ORANGE)
            y_offset += 22

        # 显示第二大矩形参考信息
        if len(max_rectangles) >= 2:
            reference_pixels = get_second_largest_rect_longest_edge(max_rectangles)
            if reference_pixels > 0:
                ref_info = f"Reference: {reference_pixels:.1f}px = {second_largest_rect_physical_length:.1f}cm"
                img_result.draw_string(8, y_offset, ref_info, image.COLOR_YELLOW)
                y_offset += 22

        # 显示框内图形统计信息
        if enable_inner_shape_stats and show_inner_shape_stats:
            # 显示三角形统计
            if inner_shapes_stats['triangles']['count'] > 0:
                tri_info = f"Triangles: {inner_shapes_stats['triangles']['count']}, Avg: {inner_shapes_stats['triangles']['avg_edge_length_physical']:.2f}cm"
                img_result.draw_string(8, y_offset, tri_info, image.COLOR_RED)
                y_offset += 22

            # 显示四边形统计
            if inner_shapes_stats['quadrilaterals']['count'] > 0:
                quad_info = f"Quads: {inner_shapes_stats['quadrilaterals']['count']}, Avg: {inner_shapes_stats['quadrilaterals']['avg_edge_length_physical']:.2f}cm"
                img_result.draw_string(8, y_offset, quad_info, image.COLOR_GREEN)
                y_offset += 22

            # 显示圆形统计
            if inner_shapes_stats['circles']['count'] > 0:
                circle_info = f"Circles: {inner_shapes_stats['circles']['count']}, Avg R: {inner_shapes_stats['circles']['avg_radius_physical']:.2f}cm"
                img_result.draw_string(8, y_offset, circle_info, image.COLOR_BLUE)
                y_offset += 22

        # 显示检测总数统计
        total_shapes = triangle_count + quadrilateral_count + circle_count + polygon_count
        if total_shapes > 0:
            shape_summary = f"Total: T{triangle_count} Q{quadrilateral_count} C{circle_count} P{polygon_count}"
            img_result.draw_string(8, y_offset, shape_summary, image.COLOR_WHITE)
            y_offset += 22

    # 只在预处理未完成时显示预处理状态（使用不同的位置避免重叠）
    if enable_preprocess and not (preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold):
        if not preprocess_started:
            preprocess_text = f"Preprocessing: Waiting (Frame {frame_count}/{preprocess_start_frame})"
        elif len(max_rectangles) < 2:
            preprocess_text = f"Preprocessing: Finding rectangles ({len(max_rectangles)}/2)"
        elif preprocess_stable_frames < preprocess_stable_threshold:
            preprocess_text = f"Preprocessing: Stabilizing ({preprocess_stable_frames}/{preprocess_stable_threshold})"
        
    # 打印帧分隔符和整理后的统计信息
    print(f"\n========== 帧 {frame_count} ==========")
    
    if enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
        # 预处理完成后的简洁输出
        total_shapes = triangle_count + quadrilateral_count + circle_count + polygon_count
        print(f"图形检测: 三角形 {triangle_count} | 四边形 {quadrilateral_count} | 圆形 {circle_count} | 多边形 {polygon_count} | 总计 {total_shapes}")

        # 简化的框内图形统计信息
        if enable_inner_shape_stats and (inner_shapes_stats['triangles']['count'] > 0 or 
                                        inner_shapes_stats['quadrilaterals']['count'] > 0 or 
                                        inner_shapes_stats['circles']['count'] > 0):
            print(f"框内统计: ", end="")
            stats_parts = []
            if inner_shapes_stats['triangles']['count'] > 0:
                stats_parts.append(f"三角形 {inner_shapes_stats['triangles']['count']}个 (平均边长 {inner_shapes_stats['triangles']['avg_edge_length_physical']:.1f}cm)")
            if inner_shapes_stats['quadrilaterals']['count'] > 0:
                stats_parts.append(f"四边形 {inner_shapes_stats['quadrilaterals']['count']}个 (平均边长 {inner_shapes_stats['quadrilaterals']['avg_edge_length_physical']:.1f}cm)")
            if inner_shapes_stats['circles']['count'] > 0:
                stats_parts.append(f"圆形 {inner_shapes_stats['circles']['count']}个 (平均半径 {inner_shapes_stats['circles']['avg_radius_physical']:.1f}cm)")
            print(" | ".join(stats_parts))

        # 距离测量信息
        if enable_distance_measurement and current_distance > 0:
            print(f"距离测量: {current_distance:.1f}cm")
    else:
        # 预处理阶段的简化输出
        total_shapes = triangle_count + quadrilateral_count + circle_count + polygon_count
        print(f"预处理中 - 图形检测: 总计 {total_shapes} 个图形")

    # 只显示必要的信息（预处理完成后），优化排版和字体大小
    if enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
        y_offset = 8  # 起始位置稍微下移

        # 显示ROI优化状态
        if use_roi_optimization and roi_valid:
            x, y, w, h = cached_roi_rect
            total_area = gray.shape[0] * gray.shape[1]
            roi_area_percent = (w*h)/total_area*100 if total_area > 0 else 0
            roi_info = f"ROI: {w}x{h} ({roi_area_percent:.0f}%)"
            img_result.draw_string(8, y_offset, roi_info, image.COLOR_GREEN)
            y_offset += 22

        # 显示距离测量信息
        if enable_distance_measurement and current_distance > 0:
            distance_info = f"Distance: {current_distance:.1f}cm"
            img_result.draw_string(8, y_offset, distance_info, image.COLOR_ORANGE)
            y_offset += 22

        # 显示第二大矩形参考信息
        if len(max_rectangles) >= 2:
            reference_pixels = get_second_largest_rect_longest_edge(max_rectangles)
            if reference_pixels > 0:
                ref_info = f"Reference: {reference_pixels:.1f}px = {second_largest_rect_physical_length:.1f}cm"
                img_result.draw_string(8, y_offset, ref_info, image.COLOR_YELLOW)
                y_offset += 22

        # 显示框内图形统计信息
        if enable_inner_shape_stats and show_inner_shape_stats:
            # 显示三角形统计
            if inner_shapes_stats['triangles']['count'] > 0:
                tri_info = f"Triangles: {inner_shapes_stats['triangles']['count']}, Avg: {inner_shapes_stats['triangles']['avg_edge_length_physical']:.2f}cm"
                img_result.draw_string(8, y_offset, tri_info, image.COLOR_RED)
                y_offset += 22

            # 显示四边形统计
            if inner_shapes_stats['quadrilaterals']['count'] > 0:
                quad_info = f"Quads: {inner_shapes_stats['quadrilaterals']['count']}, Avg: {inner_shapes_stats['quadrilaterals']['avg_edge_length_physical']:.2f}cm"
                img_result.draw_string(8, y_offset, quad_info, image.COLOR_GREEN)
                y_offset += 22

            # 显示圆形统计
            if inner_shapes_stats['circles']['count'] > 0:
                circle_info = f"Circles: {inner_shapes_stats['circles']['count']}, Avg R: {inner_shapes_stats['circles']['avg_radius_physical']:.2f}cm"
                img_result.draw_string(8, y_offset, circle_info, image.COLOR_BLUE)
                y_offset += 22

        # 显示检测总数统计
        total_shapes = triangle_count + quadrilateral_count + circle_count + polygon_count
        if total_shapes > 0:
            shape_summary = f"Total: T{triangle_count} Q{quadrilateral_count} C{circle_count} P{polygon_count}"
            img_result.draw_string(8, y_offset, shape_summary, image.COLOR_WHITE)
            y_offset += 22

    # 只在预处理未完成时显示预处理状态（使用不同的位置避免重叠）
    if enable_preprocess and not (preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold):
        if not preprocess_started:
            preprocess_text = f"Preprocessing: Waiting (Frame {frame_count}/{preprocess_start_frame})"
        elif len(max_rectangles) < 2:
            preprocess_text = f"Preprocessing: Finding rectangles ({len(max_rectangles)}/2)"
        elif preprocess_stable_frames < preprocess_stable_threshold:
            preprocess_text = f"Preprocessing: Stabilizing ({preprocess_stable_frames}/{preprocess_stable_threshold})"
        
        # 显示预处理状态，位置居中避免与其他信息重叠
        img_result.draw_string(160, 30, preprocess_text, image.COLOR_YELLOW)

    # 显示结果
    disp.show(img_result)
    
    # 在预处理完成后，突出显示两个最大矩形框
    if enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
        # 在图像上绘制两个最大矩形的轮廓，使用明显的颜色
        for i, (_, _, approx) in enumerate(max_rectangles):
            # 转换为点列表
            points = [tuple(pt[0]) for pt in approx]
            # 使用红色和蓝色分别标记两个矩形
            highlight_color = image.COLOR_RED if i == 0 else image.COLOR_BLUE
            # 绘制粗线条轮廓
            for j in range(len(points)):
                pt1 = points[j]
                pt2 = points[(j+1) % len(points)]
                img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], highlight_color, 3)
            
            # 在矩形上标注其序号
            M = cv2.moments(approx)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                img_result.draw_string(cx-30, cy, f"主矩形 {i+1}", highlight_color)

        # 绘制ROI边界（如果启用ROI优化）
        if use_roi_optimization and roi_valid:
            x, y, w, h = cached_roi_rect
            # 绘制ROI边界矩形（使用四条线段）
            img_result.draw_line(x, y, x+w, y, image.COLOR_GREEN, 2)  # 上边
            img_result.draw_line(x+w, y, x+w, y+h, image.COLOR_GREEN, 2)  # 右边
            img_result.draw_line(x+w, y+h, x, y+h, image.COLOR_GREEN, 2)  # 下边
            img_result.draw_line(x, y+h, x, y, image.COLOR_GREEN, 2)  # 左边
            # 在ROI左上角标注
            img_result.draw_string(x+5, y+5, "ROI", image.COLOR_GREEN)

        # 可视化掩码区域（半透明）
        show_mask_debug = False  # 控制是否显示掩码调试视图
        if show_mask_debug:
            mask = np.zeros_like(gray)
            for _, _, approx in max_rectangles:
                cv2.drawContours(mask, [approx], 0, 255, -1)
            
            # 转换掩码为彩色
            mask_colored = np.zeros_like(img_cv)
            mask_colored[mask > 0] = [0, 200, 0]  # 绿色
            
            # 叠加到原图
            overlay = cv2.addWeighted(img_cv, 1.0, mask_colored, 0.3, 0)
            img_overlay = image.cv2image(overlay, bgr=True, copy=False)
            
            # 显示叠加后的图像
            disp.show(img_overlay)
            time.sleep_ms(5)  # 短暂延时确保显示
        else:
            # 重新显示带有突出显示的图像
            disp.show(img_result)
    
    # 清理长时间未检测到的形状数据
    expired_positions = []
    for pos, last_seen_frame in shape_tracking_data.items():
        if frame_count - last_seen_frame > shape_timeout:
            expired_positions.append(pos)
    
    # 从跟踪数据中移除过期的形状位置
    for pos in expired_positions:
        try:
            if pos in shape_tracking_data:
                shape_type = pos[0]
                shape_tracking_data.pop(pos, None)
                print(f"清理过期形状: {shape_type} 在位置 ({pos[1]}, {pos[2]})")
                
            # 清理对应的顶点历史数据
            if pos in vertex_history:
                vertex_history.pop(pos, None)
            
            # 清理对应的边长历史数据
            edge_keys_to_remove = []
            for edge_key in edge_history:
                if edge_key[0] == pos:
                    edge_keys_to_remove.append(edge_key)
            
            for key in edge_keys_to_remove:
                edge_history.pop(key, None)
            
            # 清理对应的圆半径历史数据
            if pos in circle_radius_history:
                circle_radius_history.pop(pos, None)
        except Exception as e:
            print(f"清理形状数据时出错: {e}, 位置: {pos}")
            
    if expired_positions:
        print(f"清理了 {len(expired_positions)} 个过期形状位置")
    
    # 重置当前帧形状列表，准备下一帧
    last_frame_shapes = {}
    
    # 增加总帧计数，用于预处理和形状跟踪
    frame_count += 1
    
    # 使用单独的计数器控制视图切换
    view_switch_count += 1
    if view_switch_count >= 10:  # 每10帧切换一次视图
        view_switch_count = 0
        if show_debug:
            # 确保视图模式始终在有效范围内
            debug_view = (debug_view + 1) % 4  # 现在有4种视图模式
            print(f"切换视图模式到: {['原图', '二值图', '边缘图', '闭运算图'][debug_view]}")
    
    # 简短延时，防止程序占用过多资源
    # 使用0毫秒延时让出CPU时间片给其他任务，但不会实际暂停程序
    time.sleep_ms(0)